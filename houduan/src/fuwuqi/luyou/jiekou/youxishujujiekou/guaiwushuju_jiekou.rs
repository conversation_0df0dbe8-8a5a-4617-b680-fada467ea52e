#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::dingyii_jiekou;
use crate::fuwuqi::luyou::luyoujiegouti_chuli::mingwen_xiangying;
use crate::fuwuqi::luyou::{luy<PERSON>_qingqiu_kaishi, luyou_qingqiu_wancheng, luyou_rizhi_xinxi};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::{
    guaiwu_liebiao_guanli, guaiwu_fenlei_liebiao_guanli, guaiwu_chuliqii,
    guaiwufenleijiegouti::{fenye_canshu, mingcheng_fenlei_lianhe_sousuo_canshu}
};
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwufenleijiegouti::{
    mingcheng_sousuo_canshu, mingcheng_sousuo_leixing, mingcheng_sousuo_jiben_canshu,
    chicun, yuansu, zhongzu, biaozhi, ai
};
use rocket::http::Status;
use rocket::serde::json::Json;
use rocket::{get, options, State};
use std::sync::Arc;

/// 怪物列表接口处理函数
#[get("/guaiwu/liebiao?<yema>&<meiye_shuliang>")]
pub async fn guaiwu_liebiao_jiekou(
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = "怪物列表接口";
    let qingqiu_lujing = "/guaiwu/liebiao";
    let qingqiu_fangfa = "GET";
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(20);

    // 参数验证
    if yema < 1 {
        let xiangying = mingwen_xiangying::shibai_xiangying("页码必须大于0".to_string());
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang < 1 || meiye_shuliang > 100 {
        let xiangying = mingwen_xiangying::shibai_xiangying("每页数量必须在1-100之间".to_string());
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建怪物列表管理器
    let liebiao_guanli = if let Some(redis) = redis_guanli {
        guaiwu_liebiao_guanli::new_with_redis(mysql_guanli.as_ref().clone(), redis.as_ref().clone())
    } else {
        guaiwu_liebiao_guanli::new(mysql_guanli.as_ref().clone())
    };

    // 构建分页参数
    let fenye_canshu = fenye_canshu {
        yema,
        meiye_shuliang,
    };

    // 获取怪物列表
    match liebiao_guanli.huoqu_guaiwu_liebiao(fenye_canshu).await {
        Ok(json_result) => {
            // 解析JSON字符串为Value
            match serde_json::from_str::<serde_json::Value>(&json_result) {
                Ok(shuju) => {
                    let xiangying = mingwen_xiangying::chenggong_with_shuju(
                        "获取怪物列表成功".to_string(),
                        shuju
                    );
                    let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
                    luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                    luyou_rizhi_xinxi(jiekou_ming, &format!("获取怪物列表成功，页码: {}, 每页数量: {}", yema, meiye_shuliang));
                    Json(xiangying)
                }
                Err(e) => {
                    let xiangying = mingwen_xiangying::shibai_xiangying(format!("解析返回数据失败: {}", e));
                    let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
                    luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
                    Json(xiangying)
                }
            }
        }
        Err(e) => {
            let xiangying = mingwen_xiangying::shibai_xiangying(format!("获取怪物列表失败: {}", e));
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/guaiwu/liebiao")]
pub fn guaiwu_liebiao_yujian() -> Status {
    Status::Ok
}

/// 怪物详细信息接口处理函数
#[get("/guaiwu/xiangxi/<id>")]
pub async fn guaiwu_xiangxi_jiekou(
    id: i32,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = "怪物详细信息接口";
    let qingqiu_lujing = "/guaiwu/xiangxi";
    let qingqiu_fangfa = "GET";
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 参数验证
    if id <= 0 {
        let xiangying = mingwen_xiangying::shibai_xiangying("怪物ID必须大于0".to_string());
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建怪物数据处理器
    let chuliqii = if let Some(redis) = redis_guanli {
        guaiwu_chuliqii::new_with_redis(mysql_guanli.as_ref().clone(), redis.as_ref().clone())
    } else {
        guaiwu_chuliqii::new(mysql_guanli.as_ref().clone())
    };

    // 获取怪物详细信息
    match chuliqii.huoqu_guaiwu_hebing_quanbu_shuju(id).await {
        Ok(Some((shuju_map, _is_cached))) => {
            // 将HashMap转换为JSON Value
            match serde_json::to_value(shuju_map) {
                Ok(shuju) => {
                    let xiangying = mingwen_xiangying::chenggong_with_shuju(
                        "获取怪物详细信息成功".to_string(),
                        shuju
                    );
                    let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
                    luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                    luyou_rizhi_xinxi(jiekou_ming, &format!("获取怪物详细信息成功，ID: {}", id));
                    Json(xiangying)
                }
                Err(e) => {
                    let xiangying = mingwen_xiangying::shibai_xiangying(format!("序列化数据失败: {}", e));
                    let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
                    luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
                    Json(xiangying)
                }
            }
        }
        Ok(None) => {
            let xiangying = mingwen_xiangying::shibai_xiangying(format!("未找到ID为{}的怪物", id));
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
            Json(xiangying)
        }
        Err(e) => {
            let xiangying = mingwen_xiangying::shibai_xiangying(format!("获取怪物详细信息失败: {}", e));
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/guaiwu/xiangxi/<_id>")]
pub fn guaiwu_xiangxi_yujian(_id: i32) -> Status {
    Status::Ok
}

/// 怪物名称搜索接口处理函数
#[get("/guaiwu/sousuo/mingcheng?<guanjianci>&<sousuo_leixing>&<yema>&<meiye_shuliang>")]
pub async fn guaiwu_mingcheng_sousuo_jiekou(
    guanjianci: String,
    sousuo_leixing: Option<String>,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = "怪物名称搜索接口";
    let qingqiu_lujing = "/guaiwu/sousuo/mingcheng";
    let qingqiu_fangfa = "GET";
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(20);

    // 解析搜索类型参数，默认为模糊搜索
    let sousuo_leixing_enum = match sousuo_leixing.as_deref() {
        Some("jingque") | Some("精确") => mingcheng_sousuo_leixing::jingque,
        Some("mohu") | Some("模糊") | None => mingcheng_sousuo_leixing::mohu,
        Some(other) => {
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("无效的搜索类型: {}，支持的类型: jingque(精确), mohu(模糊)", other)
            );
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
            return Json(xiangying);
        }
    };

    // 参数验证
    if guanjianci.trim().is_empty() {
        let xiangying = mingwen_xiangying::shibai_xiangying("搜索关键词不能为空".to_string());
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 检查关键词长度，要求至少2个字符
    if guanjianci.trim().chars().count() < 2 {
        let xiangying = mingwen_xiangying::shibai_xiangying("搜索关键词必须至少包含2个字符".to_string());
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if yema < 1 {
        let xiangying = mingwen_xiangying::shibai_xiangying("页码必须大于0".to_string());
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang < 1 || meiye_shuliang > 100 {
        let xiangying = mingwen_xiangying::shibai_xiangying("每页数量必须在1-100之间".to_string());
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建怪物列表管理器
    let liebiao_guanli = if let Some(redis) = redis_guanli {
        guaiwu_liebiao_guanli::new_with_redis(mysql_guanli.as_ref().clone(), redis.as_ref().clone())
    } else {
        guaiwu_liebiao_guanli::new(mysql_guanli.as_ref().clone())
    };

    // 构建搜索参数
    let sousuo_canshu = mingcheng_sousuo_canshu {
        guanjianci: guanjianci.clone(),
        sousuo_leixing: sousuo_leixing_enum.clone(),
        yema,
        meiye_shuliang,
    };

    // 执行名称搜索
    match liebiao_guanli.mingcheng_sousuo_guaiwu_liebiao(sousuo_canshu).await {
        Ok(json_result) => {
            // 解析JSON字符串为Value
            match serde_json::from_str::<serde_json::Value>(&json_result) {
                Ok(shuju) => {
                    let xiangying = mingwen_xiangying::chenggong_with_shuju(
                        "搜索怪物成功".to_string(),
                        shuju
                    );
                    let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
                    luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                    let sousuo_leixing_str = match sousuo_leixing_enum {
                        mingcheng_sousuo_leixing::jingque => "精确",
                        mingcheng_sousuo_leixing::mohu => "模糊",
                    };
                    luyou_rizhi_xinxi(jiekou_ming, &format!("搜索怪物成功，关键词: {}, 搜索类型: {}, 页码: {}", guanjianci, sousuo_leixing_str, yema));
                    Json(xiangying)
                }
                Err(e) => {
                    let xiangying = mingwen_xiangying::shibai_xiangying(format!("解析返回数据失败: {}", e));
                    let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
                    luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
                    Json(xiangying)
                }
            }
        }
        Err(e) => {
            let xiangying = mingwen_xiangying::shibai_xiangying(format!("搜索怪物失败: {}", e));
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/guaiwu/sousuo/mingcheng")]
pub fn guaiwu_mingcheng_sousuo_yujian() -> Status {
    Status::Ok
}

/// 怪物分类列表接口处理函数
#[get("/guaiwu/fenlei/liebiao?<yema>&<meiye_shuliang>")]
pub async fn guaiwu_fenlei_liebiao_jiekou(
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = "怪物分类列表接口";
    let qingqiu_lujing = "/guaiwu/fenlei/liebiao";
    let qingqiu_fangfa = "GET";
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(20);

    // 参数验证
    if yema < 1 {
        let xiangying = mingwen_xiangying::shibai_xiangying("页码必须大于0".to_string());
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang < 1 || meiye_shuliang > 100 {
        let xiangying = mingwen_xiangying::shibai_xiangying("每页数量必须在1-100之间".to_string());
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建怪物分类列表管理器
    let fenlei_liebiao_guanli = if let Some(redis) = redis_guanli {
        guaiwu_fenlei_liebiao_guanli::new_with_redis(mysql_guanli.as_ref().clone(), redis.as_ref().clone())
    } else {
        guaiwu_fenlei_liebiao_guanli::new(mysql_guanli.as_ref().clone())
    };

    // 构建分页参数
    let fenye_canshu = crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwushuju_fenleiliebiao::fenye_canshu {
        yema,
        meiye_shuliang,
    };

    // 执行分类筛选（获取所有怪物的分类列表）
    match fenlei_liebiao_guanli.huoqu_fenlei_guaiwu_liebiao(fenye_canshu).await {
        Ok(json_result) => {
            // 解析JSON字符串为Value
            match serde_json::from_str::<serde_json::Value>(&json_result) {
                Ok(shuju) => {
                    let xiangying = mingwen_xiangying::chenggong_with_shuju(
                        "获取怪物分类列表成功".to_string(),
                        shuju
                    );
                    let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
                    luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                    luyou_rizhi_xinxi(jiekou_ming, &format!("获取怪物分类列表成功，页码: {}", yema));
                    Json(xiangying)
                }
                Err(e) => {
                    let xiangying = mingwen_xiangying::shibai_xiangying(format!("解析返回数据失败: {}", e));
                    let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
                    luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
                    Json(xiangying)
                }
            }
        }
        Err(e) => {
            let xiangying = mingwen_xiangying::shibai_xiangying(format!("获取怪物分类列表失败: {}", e));
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/guaiwu/fenlei/liebiao")]
pub fn guaiwu_fenlei_liebiao_yujian() -> Status {
    Status::Ok
}

/// 怪物联合搜索接口处理函数（支持名称+分类组合搜索）
#[get("/guaiwu/lianhe_sousuo?<mingcheng_guanjianci>&<sousuo_leixing>&<chicun_shaixuan>&<yuansu_shaixuan>&<zhongzu_shaixuan>&<biaozhi_shaixuan>&<ai_shaixuan>&<yema>&<meiye_shuliang>")]
pub async fn guaiwu_lianhe_sousuo_jiekou(
    mingcheng_guanjianci: Option<String>,
    sousuo_leixing: Option<String>,
    chicun_shaixuan: Option<String>,
    yuansu_shaixuan: Option<String>,
    zhongzu_shaixuan: Option<String>,
    biaozhi_shaixuan: Option<String>,
    ai_shaixuan: Option<String>,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = "怪物联合搜索接口";
    let qingqiu_lujing = "/guaiwu/lianhe_sousuo";
    let qingqiu_fangfa = "GET";
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(20);

    // 解析搜索类型参数，默认为模糊搜索
    let sousuo_leixing_enum = match sousuo_leixing.as_deref() {
        Some("jingque") | Some("精确") => mingcheng_sousuo_leixing::jingque,
        Some("mohu") | Some("模糊") | None => mingcheng_sousuo_leixing::mohu,
        Some(other) => {
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("无效的搜索类型: {}，支持的类型: jingque(精确), mohu(模糊)", other)
            );
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
            return Json(xiangying);
        }
    };

    // 解析尺寸筛选参数
    let chicun_shaixuan_enum = match chicun_shaixuan.as_deref() {
        Some("small") | Some("小型") => Some(chicun::small),
        Some("medium") | Some("中型") => Some(chicun::medium),
        Some("large") | Some("大型") => Some(chicun::large),
        Some("weizhi") | Some("未知") => Some(chicun::weizhi),
        None => None,
        Some(other) => {
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("无效的尺寸类型: {}，支持的类型: small(小型), medium(中型), large(大型), weizhi(未知)", other)
            );
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
            return Json(xiangying);
        }
    };

    // 解析元素筛选参数
    let yuansu_shaixuan_enum = match yuansu_shaixuan.as_deref() {
        Some("wu") | Some("无") => Some(yuansu::wu),
        Some("shui") | Some("水") => Some(yuansu::shui),
        Some("di") | Some("地") => Some(yuansu::di),
        Some("huo") | Some("火") => Some(yuansu::huo),
        Some("feng") | Some("风") => Some(yuansu::feng),
        Some("du") | Some("毒") => Some(yuansu::du),
        Some("sheng") | Some("圣") => Some(yuansu::sheng),
        Some("an") | Some("暗") => Some(yuansu::an),
        Some("nian") | Some("念") => Some(yuansu::nian),
        Some("busi") | Some("不死") => Some(yuansu::busi),
        Some("weizhi") | Some("未知") => Some(yuansu::weizhi),
        None => None,
        Some(other) => {
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("无效的元素类型: {}，支持的类型: wu(无), shui(水), di(地), huo(火), feng(风), du(毒), sheng(圣), an(暗), nian(念), busi(不死), weizhi(未知)", other)
            );
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
            return Json(xiangying);
        }
    };

    // 解析种族筛选参数
    let zhongzu_shaixuan_enum = match zhongzu_shaixuan.as_deref() {
        Some("formless") | Some("无形") => Some(zhongzu::formless),
        Some("undead") | Some("不死") => Some(zhongzu::undead),
        Some("brute") | Some("动物") => Some(zhongzu::brute),
        Some("plant") | Some("植物") => Some(zhongzu::plant),
        Some("insect") | Some("昆虫") => Some(zhongzu::insect),
        Some("fish") | Some("鱼类") => Some(zhongzu::fish),
        Some("demon") | Some("恶魔") => Some(zhongzu::demon),
        Some("human") | Some("人类") => Some(zhongzu::human),
        Some("angel") | Some("天使") => Some(zhongzu::angel),
        Some("dragon") | Some("龙族") => Some(zhongzu::dragon),
        Some("weizhi") | Some("未知") => Some(zhongzu::weizhi),
        None => None,
        Some(other) => {
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("无效的种族类型: {}，支持的类型: formless(无形), undead(不死), brute(动物), plant(植物), insect(昆虫), fish(鱼类), demon(恶魔), human(人类), angel(天使), dragon(龙族), weizhi(未知)", other)
            );
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
            return Json(xiangying);
        }
    };

    // 解析标志筛选参数
    let biaozhi_shaixuan_enum = match biaozhi_shaixuan.as_deref() {
        Some("normal") | Some("普通") => Some(biaozhi::normal),
        Some("champion") | Some("精英") => Some(biaozhi::champion),
        Some("boss") | Some("BOSS") => Some(biaozhi::boss),
        Some("mvp") | Some("MVP") => Some(biaozhi::mvp),
        Some("weizhi") | Some("未知") => Some(biaozhi::weizhi),
        None => None,
        Some(other) => {
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("无效的标志类型: {}，支持的类型: normal(普通), champion(精英), boss(BOSS), mvp(MVP), weizhi(未知)", other)
            );
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
            return Json(xiangying);
        }
    };

    // 解析AI筛选参数（支持多个，用逗号分隔）
    let ai_shaixuan_enum = if let Some(ai_str) = ai_shaixuan {
        let mut ai_vec = Vec::new();
        for ai_item in ai_str.split(',') {
            let ai_item = ai_item.trim();
            match ai_item {
                "aggressive" | "主动攻击" => ai_vec.push(ai::aggressive),
                "assist" | "协助" => ai_vec.push(ai::assist),
                "looter" | "掠夺" => ai_vec.push(ai::looter),
                "cast_sensor" | "感知施法" => ai_vec.push(ai::cast_sensor),
                "immobile" | "不移动" => ai_vec.push(ai::immobile),
                "weizhi" | "未知" => ai_vec.push(ai::weizhi),
                other if !other.is_empty() => {
                    let xiangying = mingwen_xiangying::shibai_xiangying(
                        format!("无效的AI类型: {}，支持的类型: aggressive(主动攻击), assist(协助), looter(掠夺), cast_sensor(感知施法), immobile(不移动), weizhi(未知)", other)
                    );
                    let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
                    luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
                    return Json(xiangying);
                }
                _ => {} // 忽略空字符串
            }
        }
        if ai_vec.is_empty() { None } else { Some(ai_vec) }
    } else {
        None
    };

    // 参数验证
    if yema < 1 {
        let xiangying = mingwen_xiangying::shibai_xiangying("页码必须大于0".to_string());
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang < 1 || meiye_shuliang > 100 {
        let xiangying = mingwen_xiangying::shibai_xiangying("每页数量必须在1-100之间".to_string());
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建怪物分类列表管理器
    let fenlei_liebiao_guanli = if let Some(redis) = redis_guanli {
        guaiwu_fenlei_liebiao_guanli::new_with_redis(mysql_guanli.as_ref().clone(), redis.as_ref().clone())
    } else {
        guaiwu_fenlei_liebiao_guanli::new(mysql_guanli.as_ref().clone())
    };

    // 构建简化的联合搜索参数（只支持名称搜索）
    let mingcheng_sousuo = if let Some(guanjianci) = mingcheng_guanjianci {
        // 检查关键词长度，要求至少2个字符
        if guanjianci.trim().chars().count() < 2 {
            let xiangying = mingwen_xiangying::shibai_xiangying("搜索关键词必须至少包含2个字符".to_string());
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
            return Json(xiangying);
        }
        Some(mingcheng_sousuo_jiben_canshu {
            guanjianci,
            sousuo_leixing: sousuo_leixing_enum.clone(),
        })
    } else {
        None
    };

    let lianhe_sousuo_canshu = mingcheng_fenlei_lianhe_sousuo_canshu {
        mingcheng_sousuo,
        chicun_shaixuan: chicun_shaixuan_enum,
        yuansu_shaixuan: yuansu_shaixuan_enum,
        zhongzu_shaixuan: zhongzu_shaixuan_enum,
        biaozhi_shaixuan: biaozhi_shaixuan_enum,
        ai_shaixuan: ai_shaixuan_enum,
        yema,
        meiye_shuliang,
    };

    // 执行联合搜索
    match fenlei_liebiao_guanli.mingcheng_fenlei_lianhe_sousuo_guaiwu_liebiao(lianhe_sousuo_canshu).await {
        Ok(json_result) => {
            // 解析JSON字符串为Value
            match serde_json::from_str::<serde_json::Value>(&json_result) {
                Ok(shuju) => {
                    let xiangying = mingwen_xiangying::chenggong_with_shuju(
                        "怪物联合搜索成功".to_string(),
                        shuju
                    );
                    let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
                    luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                    luyou_rizhi_xinxi(jiekou_ming, &format!("怪物联合搜索成功，页码: {}", yema));
                    Json(xiangying)
                }
                Err(e) => {
                    let xiangying = mingwen_xiangying::shibai_xiangying(format!("解析返回数据失败: {}", e));
                    let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
                    luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
                    Json(xiangying)
                }
            }
        }
        Err(e) => {
            let xiangying = mingwen_xiangying::shibai_xiangying(format!("怪物联合搜索失败: {}", e));
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/guaiwu/lianhe_sousuo")]
pub fn guaiwu_lianhe_sousuo_yujian() -> Status {
    Status::Ok
}

// 使用宏定义接口
dingyii_jiekou!(
    guaiwushuju_jiekou,
    lujing: "/guaiwu",
    fangfa: "GET",
    miaoshu: "怪物数据接口",
    jieshao: "提供怪物数据的获取、搜索和分类筛选功能",
    routes: [
        guaiwu_liebiao_jiekou, guaiwu_liebiao_yujian,
        guaiwu_xiangxi_jiekou, guaiwu_xiangxi_yujian,
        guaiwu_mingcheng_sousuo_jiekou, guaiwu_mingcheng_sousuo_yujian,
        guaiwu_fenlei_liebiao_jiekou, guaiwu_fenlei_liebiao_yujian,
        guaiwu_lianhe_sousuo_jiekou, guaiwu_lianhe_sousuo_yujian
    ]
);