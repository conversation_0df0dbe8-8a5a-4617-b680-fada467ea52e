#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::shujuku_rizhiguanli::{shujukuxitong_rizhi_cuowu, shujukuxitong_rizhi_xinxi};

/// 怪物数据处理日志字符串管理类 - 只记录错误日志
pub struct guaiwushujuchuli_rizhi;

impl guaiwushujuchuli_rizhi {
    /// 模块名称常量
    const mokuai_ming: &'static str = "怪物数据库操作";

    // ==================== 错误日志方法 ====================

    /// 获取怪物汇总数据失败
    pub fn huoqu_guaiwu_huizong_shuju_shibai(id: i32, ziduan_ming: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("获取怪物汇总数据失败，id: {}，字段: {}，错误: {}", id, ziduan_ming, cuowu));
    }

    /// 获取怪物名称数据失败
    pub fn huoqu_guaiwu_mingcheng_shuju_shibai(id: i32, ziduan_ming: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("获取怪物名称数据失败，id: {}，字段: {}，错误: {}", id, ziduan_ming, cuowu));
    }

    /// 获取怪物汇总全部数据失败
    pub fn huoqu_guaiwu_huizong_quanbu_shuju_shibai(id: i32, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("获取怪物汇总全部数据失败，id: {}，错误: {}", id, cuowu));
    }

    /// 获取怪物名称全部数据失败
    pub fn huoqu_guaiwu_mingcheng_quanbu_shuju_shibai(id: i32, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("获取怪物名称全部数据失败，id: {}，错误: {}", id, cuowu));
    }

    /// 获取怪物合并全部数据失败
    pub fn huoqu_guaiwu_hebing_quanbu_shuju_shibai(id: i32, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("获取怪物合并全部数据失败，id: {}，错误: {}", id, cuowu));
    }

    /// 存储Redis缓存失败
    pub fn cunchu_redis_huancun_shibai(id: i32, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("存储Redis缓存失败，id: {}，错误: {}", id, cuowu));
    }

    /// 获取怪物总数量失败
    pub fn huoqu_guaiwu_zong_shuliang_shibai(cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("获取怪物总数量失败，错误: {}", cuowu));
    }

    /// 获取怪物ID列表失败
    pub fn huoqu_guaiwu_id_liebiao_shibai(yema: u32, meiye_shuliang: u32, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("获取怪物ID列表失败，页码: {}，每页数量: {}，错误: {}", yema, meiye_shuliang, cuowu));
    }

    /// 获取怪物名称信息失败
    pub fn huoqu_guaiwu_mingcheng_xinxi_shibai(id: i32, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("获取怪物名称信息失败，id: {}，错误: {}", id, cuowu));
    }

    /// 怪物列表分页查询失败
    pub fn guaiwu_liebiao_fenye_chaxun_shibai(yema: u32, meiye_shuliang: u32, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("怪物列表分页查询失败，页码: {}，每页数量: {}，错误: {}", yema, meiye_shuliang, cuowu));
    }

    /// 怪物列表参数验证失败
    pub fn guaiwu_liebiao_canshu_yanzheng_shibai(yema: u32, meiye_shuliang: u32, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("怪物列表参数验证失败，页码: {}，每页数量: {}，错误: {}", yema, meiye_shuliang, cuowu));
    }

    /// 搜索mob_name表失败
    pub fn sousuo_mob_name_biao_shibai(yema: u32, meiye_shuliang: u32, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("搜索mob_name表失败，页码: {}，每页数量: {}，错误: {}", yema, meiye_shuliang, cuowu));
    }

    /// 搜索huizong表失败
    pub fn sousuo_huizong_biao_shibai(yema: u32, meiye_shuliang: u32, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("搜索huizong表失败，页码: {}，每页数量: {}，错误: {}", yema, meiye_shuliang, cuowu));
    }

    /// 并发搜索失败
    pub fn bingfa_sousuo_shibai(yema: u32, meiye_shuliang: u32, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("并发搜索失败，页码: {}，每页数量: {}，错误: {}", yema, meiye_shuliang, cuowu));
    }

    /// 获取总数量失败
    pub fn huoqu_zong_shuliang_shibai(yema: u32, meiye_shuliang: u32, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("获取总数量失败，页码: {}，每页数量: {}，错误: {}", yema, meiye_shuliang, cuowu));
    }

    /// 获取ID列表失败
    pub fn huoqu_id_liebiao_shibai(yema: u32, meiye_shuliang: u32, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("获取ID列表失败，页码: {}，每页数量: {}，错误: {}", yema, meiye_shuliang, cuowu));
    }

    /// 获取怪物名称信息失败（带ID）
    pub fn huoqu_guaiwu_mingcheng_xinxi_shibai_with_id(id: i32, yema: u32, meiye_shuliang: u32, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("获取怪物{}名称信息失败，页码: {}，每页数量: {}，错误: {}", id, yema, meiye_shuliang, cuowu));
    }

    /// 获取怪物分类信息失败（带ID）
    pub fn huoqu_guaiwu_fenlei_xinxi_shibai_with_id(id: i32, yema: u32, meiye_shuliang: u32, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("获取怪物{}分类信息失败，页码: {}，每页数量: {}，错误: {}", id, yema, meiye_shuliang, cuowu));
    }


}
