#![allow(non_camel_case_types)]
#![allow(dead_code)]

use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use super::guaiwushujuchuli_rizhi::guaiwushujuchuli_rizhi;
use sqlx::Row;
use serde_json;

/// 怪物尺寸
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum chicun {
    small,       // 对应数据库 chicun_small
    medium,      // 对应数据库 chicun_medium
    large,       // 对应数据库 chicun_large
    weizhi,      // 对应数据库 chicun_weizhi
}

/// 怪物元素
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum yuansu {
    wu,
    shui,
    di,
    huo,
    feng,
    du,
    sheng,
    an,
    nian,
    busi,
    weizhi,
}

/// 怪物种族
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum zhongzu {
    formless,    // 对应数据库 zhongzu_formless
    undead,      // 对应数据库 zhongzu_undead
    brute,       // 对应数据库 zhongzu_brute
    plant,       // 对应数据库 zhongzu_plant
    insect,      // 对应数据库 zhongzu_insect
    fish,        // 对应数据库 zhongzu_fish
    demon,       // 对应数据库 zhongzu_demon
    human,       // 对应数据库 zhongzu_human
    angel,       // 对应数据库 zhongzu_angel
    dragon,      // 对应数据库 zhongzu_dragon
    weizhi,      // 对应数据库 zhongzu_weizhi
}

/// 怪物标志
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum biaozhi {
    normal,      // 对应数据库 biaozhi_normal
    champion,    // 对应数据库 biaozhi_champion
    boss,        // 对应数据库 biaozhi_boss
    mvp,         // 对应数据库 biaozhi_mvp
    weizhi,      // 对应数据库 biaozhi_weizhi
}

/// 怪物AI行为
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ai {
    aggressive,    // 对应数据库 ai_aggressive
    assist,        // 对应数据库 ai_assist
    looter,        // 对应数据库 ai_looter
    cast_sensor,   // 对应数据库 ai_cast_sensor
    immobile,      // 对应数据库 ai_immobile
    weizhi,        // 对应数据库 ai_weizhi
}

/// 怪物分类信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct guaiwushujufenleijiegou {
    pub chicun: chicun,
    pub yuansu: yuansu,
    pub zhongzu: zhongzu,
    pub biaozhi: biaozhi,
    pub ai: Vec<ai>,
}

/// 怪物分类数据解析工具类
pub struct guaiwufenleijiexi;

impl guaiwufenleijiexi {
    /// 从数据库字段值解析尺寸
    pub fn jiexi_chicun(shuju_map: &HashMap<String, String>) -> chicun {
        if let Some(small) = shuju_map.get("chicun_small") {
            if small == "1" || small == "true" {
                return chicun::small;
            }
        }
        if let Some(medium) = shuju_map.get("chicun_medium") {
            if medium == "1" || medium == "true" {
                return chicun::medium;
            }
        }
        if let Some(large) = shuju_map.get("chicun_large") {
            if large == "1" || large == "true" {
                return chicun::large;
            }
        }
        chicun::weizhi
    }

    /// 从数据库字段值解析元素
    pub fn jiexi_yuansu(shuju_map: &HashMap<String, String>) -> yuansu {
        let yuansu_liebiao = [
            ("yuansu_wu", yuansu::wu),
            ("yuansu_shui", yuansu::shui),
            ("yuansu_di", yuansu::di),
            ("yuansu_huo", yuansu::huo),
            ("yuansu_feng", yuansu::feng),
            ("yuansu_du", yuansu::du),
            ("yuansu_sheng", yuansu::sheng),
            ("yuansu_an", yuansu::an),
            ("yuansu_nian", yuansu::nian),
            ("yuansu_busi", yuansu::busi),
        ];

        for (ziduan_ming, yuansu_leixing) in yuansu_liebiao {
            if let Some(zhi) = shuju_map.get(ziduan_ming) {
                if zhi == "1" || zhi == "true" {
                    return yuansu_leixing;
                }
            }
        }
        yuansu::weizhi
    }

    /// 从数据库字段值解析种族
    pub fn jiexi_zhongzu(shuju_map: &HashMap<String, String>) -> zhongzu {
        let zhongzu_liebiao = [
            ("zhongzu_formless", zhongzu::formless),
            ("zhongzu_undead", zhongzu::undead),
            ("zhongzu_brute", zhongzu::brute),
            ("zhongzu_plant", zhongzu::plant),
            ("zhongzu_insect", zhongzu::insect),
            ("zhongzu_fish", zhongzu::fish),
            ("zhongzu_demon", zhongzu::demon),
            ("zhongzu_human", zhongzu::human),
            ("zhongzu_angel", zhongzu::angel),
            ("zhongzu_dragon", zhongzu::dragon),
        ];

        for (ziduan_ming, zhongzu_leixing) in zhongzu_liebiao {
            if let Some(zhi) = shuju_map.get(ziduan_ming) {
                if zhi == "1" || zhi == "true" {
                    return zhongzu_leixing;
                }
            }
        }
        zhongzu::weizhi
    }

    /// 从数据库字段值解析标志
    pub fn jiexi_biaozhi(shuju_map: &HashMap<String, String>) -> biaozhi {
        let biaozhi_liebiao = [
            ("biaozhi_normal", biaozhi::normal),
            ("biaozhi_champion", biaozhi::champion),
            ("biaozhi_boss", biaozhi::boss),
            ("biaozhi_mvp", biaozhi::mvp),
        ];

        for (ziduan_ming, biaozhi_leixing) in biaozhi_liebiao {
            if let Some(zhi) = shuju_map.get(ziduan_ming) {
                if zhi == "1" || zhi == "true" {
                    return biaozhi_leixing;
                }
            }
        }
        biaozhi::weizhi
    }

    /// 从数据库字段值解析AI行为
    pub fn jiexi_ai(shuju_map: &HashMap<String, String>) -> Vec<ai> {
        let mut ai_liebiao = Vec::new();

        let ai_ziduan_liebiao = [
            ("ai_aggressive", ai::aggressive),
            ("ai_assist", ai::assist),
            ("ai_looter", ai::looter),
            ("ai_cast_sensor", ai::cast_sensor),
            ("ai_immobile", ai::immobile),
        ];

        for (ziduan_ming, ai_leixing) in ai_ziduan_liebiao {
            if let Some(zhi) = shuju_map.get(ziduan_ming) {
                if zhi == "1" || zhi == "true" {
                    ai_liebiao.push(ai_leixing);
                }
            }
        }

        if ai_liebiao.is_empty() {
            ai_liebiao.push(ai::weizhi);
        }

        ai_liebiao
    }

    /// 构建分类信息结构体
    pub fn gouzao_fenlei_xinxi(shuju_map: &HashMap<String, String>) -> guaiwushujufenleijiegou {
        guaiwushujufenleijiegou {
            chicun: Self::jiexi_chicun(shuju_map),
            yuansu: Self::jiexi_yuansu(shuju_map),
            zhongzu: Self::jiexi_zhongzu(shuju_map),
            biaozhi: Self::jiexi_biaozhi(shuju_map),
            ai: Self::jiexi_ai(shuju_map),
        }
    }

    /// 获取尺寸的中文描述
    pub fn huoqu_chicun_miaoshu(chicun: &chicun) -> &'static str {
        match chicun {
            chicun::small => "小型",
            chicun::medium => "中型",
            chicun::large => "大型",
            chicun::weizhi => "未知尺寸",
        }
    }

    /// 获取元素的中文描述
    pub fn huoqu_yuansu_miaoshu(yuansu: &yuansu) -> &'static str {
        match yuansu {
            yuansu::wu => "无属性",
            yuansu::shui => "水属性",
            yuansu::di => "地属性",
            yuansu::huo => "火属性",
            yuansu::feng => "风属性",
            yuansu::du => "毒属性",
            yuansu::sheng => "圣属性",
            yuansu::an => "暗属性",
            yuansu::nian => "念属性",
            yuansu::busi => "不死属性",
            yuansu::weizhi => "未知属性",
        }
    }

    /// 获取种族的中文描述
    pub fn huoqu_zhongzu_miaoshu(zhongzu: &zhongzu) -> &'static str {
        match zhongzu {
            zhongzu::formless => "无形种族",
            zhongzu::undead => "不死种族",
            zhongzu::brute => "动物种族",
            zhongzu::plant => "植物种族",
            zhongzu::insect => "昆虫种族",
            zhongzu::fish => "鱼类种族",
            zhongzu::demon => "恶魔种族",
            zhongzu::human => "人类种族",
            zhongzu::angel => "天使种族",
            zhongzu::dragon => "龙族",
            zhongzu::weizhi => "未知种族",
        }
    }

    /// 获取标志的中文描述
    pub fn huoqu_biaozhi_miaoshu(biaozhi: &biaozhi) -> &'static str {
        match biaozhi {
            biaozhi::normal => "普通怪物",
            biaozhi::champion => "精英怪物",
            biaozhi::boss => "BOSS",
            biaozhi::mvp => "MVP",
            biaozhi::weizhi => "未知标志",
        }
    }

    /// 获取AI行为的中文描述
    pub fn huoqu_ai_miaoshu(ai: &ai) -> &'static str {
        match ai {
            ai::aggressive => "主动攻击",
            ai::assist => "协助",
            ai::looter => "掠夺",
            ai::cast_sensor => "感知施法",
            ai::immobile => "不移动",
            ai::weizhi => "未知AI",
        }
    }

    /// 获取搜索类型的中文描述
    pub fn huoqu_leiming_jingque_miaoshu(guanjianci: &str) -> String {
        format!("类名精确匹配: {}", guanjianci)
    }

    pub fn huoqu_zhongwenming_jingque_miaoshu(guanjianci: &str) -> String {
        format!("中文名称精确匹配: {}", guanjianci)
    }

    pub fn huoqu_leiming_mohu_miaoshu(guanjianci: &str) -> String {
        format!("类名模糊匹配: {}", guanjianci)
    }

    pub fn huoqu_zhongwenming_mohu_miaoshu(guanjianci: &str) -> String {
        format!("中文名称模糊匹配: {}", guanjianci)
    }

    /// 获取通用描述字符串
    pub fn huoqu_tongyong_miaoshu() -> (&'static str, &'static str) {
        ("全部怪物", "AI")
    }
}

// ==================== 统一的数据结构定义 ====================

/// 怪物数据项（统一结构体，替代重复定义）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct guaiwu_shuju_xiang {
    /// 怪物ID
    pub id: i32,
    /// 类名（Aegis_name或dbname）
    pub leiming: String,
    /// 名称（schinese或zhongwenming）
    pub mingcheng: String,
    /// 分类信息（可选）
    pub fenlei_xinxi: Option<guaiwushujufenleijiegou>,
}

/// 分页参数（统一结构体）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct fenye_canshu {
    /// 页码（从1开始）
    pub yema: u32,
    /// 每页数量
    pub meiye_shuliang: u32,
}

/// 分页结果（统一结构体）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct fenye_jieguo {
    /// 怪物数据列表
    pub guaiwu_liebiao: Vec<guaiwu_shuju_xiang>,
    /// 当前页码
    pub dangqian_yema: u32,
    /// 每页数量
    pub meiye_shuliang: u32,
    /// 总数量
    pub zong_shuliang: u64,
    /// 总页数
    pub zong_yeshu: u32,
    /// 搜索条件摘要（可选）
    pub sousuo_tiaojian: Option<String>,
}

/// 名称搜索类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum mingcheng_sousuo_leixing {
    /// 精确匹配
    jingque,
    /// 模糊匹配
    mohu,
}

/// 名称搜索参数（基础参数，不包含分页）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct mingcheng_sousuo_jiben_canshu {
    /// 关键词
    pub guanjianci: String,
    /// 搜索类型
    pub sousuo_leixing: mingcheng_sousuo_leixing,
}

/// 名称搜索参数（包含分页）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct mingcheng_sousuo_canshu {
    /// 关键词
    pub guanjianci: String,
    /// 搜索类型
    pub sousuo_leixing: mingcheng_sousuo_leixing,
    /// 页码（从1开始）
    pub yema: u32,
    /// 每页数量
    pub meiye_shuliang: u32,
}

/// 分类搜索参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct fenlei_sousuo_canshu {
    /// 尺寸筛选
    pub chicun_shaixuan: Option<chicun>,
    /// 元素筛选
    pub yuansu_shaixuan: Option<yuansu>,
    /// 种族筛选
    pub zhongzu_shaixuan: Option<zhongzu>,
    /// 标志筛选
    pub biaozhi_shaixuan: Option<biaozhi>,
    /// AI行为筛选
    pub ai_shaixuan: Option<Vec<ai>>,
}

/// 综合搜索参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct zonghe_sousuo_canshu {
    /// 分页参数
    pub fenye_canshu: fenye_canshu,
    /// 名称搜索参数（可选）
    pub mingcheng_sousuo: Option<mingcheng_sousuo_canshu>,
    /// 分类搜索参数（可选）
    pub fenlei_sousuo: Option<fenlei_sousuo_canshu>,
}

/// 名称与分类联合搜索参数（统一结构体）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct mingcheng_fenlei_lianhe_sousuo_canshu {
    /// 名称搜索参数（可选）
    pub mingcheng_sousuo: Option<mingcheng_sousuo_jiben_canshu>,
    /// 尺寸筛选（可选）
    pub chicun_shaixuan: Option<chicun>,
    /// 元素筛选（可选）
    pub yuansu_shaixuan: Option<yuansu>,
    /// 种族筛选（可选）
    pub zhongzu_shaixuan: Option<zhongzu>,
    /// 标志筛选（可选）
    pub biaozhi_shaixuan: Option<biaozhi>,
    /// AI行为筛选（可选，支持多个）
    pub ai_shaixuan: Option<Vec<ai>>,
    /// 页码（从1开始）
    pub yema: u32,
    /// 每页数量
    pub meiye_shuliang: u32,
}

/// 怪物搜索结果项（统一结构体）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct guaiwu_sousuo_jieguo_xiang {
    /// 怪物ID
    pub id: i32,
    /// 类名（Aegis_name或dbname）
    pub leiming: String,
    /// 名称（schinese或zhongwenming）
    pub mingcheng: String,
    /// 分类信息（可选）
    pub fenlei_xinxi: Option<guaiwushujufenleijiegou>,
}

/// 怪物搜索分页结果（统一结构体）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct guaiwu_sousuo_fenye_jieguo {
    /// 怪物搜索结果列表
    pub guaiwu_liebiao: Vec<guaiwu_sousuo_jieguo_xiang>,
    /// 当前页码
    pub dangqian_yema: u32,
    /// 每页数量
    pub meiye_shuliang: u32,
    /// 总数量
    pub zong_shuliang: u64,
    /// 总页数
    pub zong_yeshu: u32,
    /// 搜索条件摘要
    pub sousuo_tiaojian: String,
}

// ==================== 通用数据访问层 ====================

/// 通用数据访问工具类
pub struct guaiwu_tongyong_shujufangwen;

impl guaiwu_tongyong_shujufangwen {
    /// 参数验证 - 分页参数
    pub fn yanzheng_fenye_canshu(canshu: &fenye_canshu) -> anyhow::Result<()> {
        if canshu.yema == 0 {
            let cuowu_xinxi = "页码必须大于0";
            guaiwushujuchuli_rizhi::guaiwu_liebiao_canshu_yanzheng_shibai(
                canshu.yema,
                canshu.meiye_shuliang,
                cuowu_xinxi
            );
            return Err(anyhow::anyhow!(cuowu_xinxi));
        }
        if canshu.meiye_shuliang == 0 || canshu.meiye_shuliang > 100 {
            let cuowu_xinxi = "每页数量必须在1-100之间";
            guaiwushujuchuli_rizhi::guaiwu_liebiao_canshu_yanzheng_shibai(
                canshu.yema,
                canshu.meiye_shuliang,
                cuowu_xinxi
            );
            return Err(anyhow::anyhow!(cuowu_xinxi));
        }
        Ok(())
    }

    /// 获取怪物总数量
    pub async fn huoqu_guaiwu_zong_shuliang(
        mysql_lianjie: &mysql_lianjie_guanli,
        redis_lianjie: Option<&redis_lianjie_guanli>
    ) -> anyhow::Result<u64> {
        let redis_jian = "guaiwu_zong_shuliang";

        // 先尝试从Redis获取缓存数据
        if let Some(redis) = redis_lianjie {
            if let Ok(Some(huancun_shuju)) = redis.huoqu(redis_jian).await {
                if let Ok(zong_shuliang) = huancun_shuju.parse::<u64>() {
                    return Ok(zong_shuliang);
                }
            }
        }

        // 从数据库获取总数
        let sql = "SELECT COUNT(*) as zong_shuliang FROM guaiwu_huizong";

        match sqlx::query(sql)
            .fetch_one(mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(row) => {
                let zong_shuliang: i64 = row.try_get("zong_shuliang")?;
                let zong_shuliang = zong_shuliang as u64;

                // 缓存到Redis，设置3天过期
                if let Some(redis) = redis_lianjie {
                    let _ = redis.shezhi_with_guoqi(redis_jian, &zong_shuliang.to_string(), 259200).await;
                }

                Ok(zong_shuliang)
            }
            Err(e) => {
                guaiwushujuchuli_rizhi::huoqu_guaiwu_zong_shuliang_shibai(&e.to_string());
                Err(e.into())
            }
        }
    }

    /// 根据分页参数获取怪物ID列表
    pub async fn huoqu_guaiwu_id_liebiao(
        mysql_lianjie: &mysql_lianjie_guanli,
        fenye_canshu: &fenye_canshu
    ) -> anyhow::Result<Vec<i32>> {
        // 计算偏移量
        let pianyi = (fenye_canshu.yema - 1) * fenye_canshu.meiye_shuliang;

        // 构建分页查询SQL
        let sql = "SELECT id FROM guaiwu_huizong ORDER BY id LIMIT ? OFFSET ?";

        match sqlx::query(sql)
            .bind(fenye_canshu.meiye_shuliang)
            .bind(pianyi)
            .fetch_all(mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(rows) => {
                let mut id_liebiao = Vec::new();
                for row in rows {
                    let id: i32 = row.try_get("id")?;
                    id_liebiao.push(id);
                }
                Ok(id_liebiao)
            }
            Err(e) => {
                guaiwushujuchuli_rizhi::huoqu_id_liebiao_shibai(
                    fenye_canshu.yema,
                    fenye_canshu.meiye_shuliang,
                    &e.to_string()
                );
                Err(e.into())
            }
        }
    }

    /// 根据ID获取怪物名称信息
    pub async fn huoqu_guaiwu_mingcheng_xinxi(
        mysql_lianjie: &mysql_lianjie_guanli,
        id: i32
    ) -> anyhow::Result<(String, String)> {
        // 首先从huizong表获取类名
        let huizong_sql = "SELECT dbname FROM guaiwu_huizong WHERE id = ?";

        let leiming = match sqlx::query(huizong_sql)
            .bind(id)
            .fetch_optional(mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(Some(row)) => {
                let dbname: Option<String> = row.try_get("dbname").unwrap_or(None);
                dbname.unwrap_or_default()
            }
            Ok(None) => String::new(),
            Err(e) => {
                guaiwushujuchuli_rizhi::huoqu_guaiwu_mingcheng_xinxi_shibai(id, &e.to_string());
                return Err(e.into());
            }
        };

        // 然后从mob_name表获取中文名字
        let mob_name_sql = "SELECT schinese FROM mob_name WHERE ID = ?";

        let mingcheng = match sqlx::query(mob_name_sql)
            .bind(id)
            .fetch_optional(mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(Some(row)) => {
                let schinese: Option<String> = row.try_get("schinese").unwrap_or(None);
                schinese.unwrap_or_default()
            }
            Ok(None) => {
                // 如果mob_name表没有数据，尝试从huizong表获取中文名
                let huizong_mingcheng_sql = "SELECT zhongwenming FROM guaiwu_huizong WHERE id = ?";
                match sqlx::query(huizong_mingcheng_sql)
                    .bind(id)
                    .fetch_optional(mysql_lianjie.huoqu_lianjiechi()?)
                    .await
                {
                    Ok(Some(row)) => {
                        let zhongwenming: Option<String> = row.try_get("zhongwenming").unwrap_or(None);
                        zhongwenming.unwrap_or_default()
                    }
                    Ok(None) => String::new(),
                    Err(_) => String::new(),
                }
            }
            Err(e) => {
                guaiwushujuchuli_rizhi::huoqu_guaiwu_mingcheng_xinxi_shibai(id, &e.to_string());
                return Err(e.into());
            }
        };

        Ok((leiming, mingcheng))
    }

    /// 生成分页缓存键
    pub fn shengcheng_fenye_huancun_jian(yema: u32, meiye_shuliang: u32) -> String {
        format!("guaiwu_liebiao:{}:{}", yema, meiye_shuliang)
    }

    /// 从Redis获取分页缓存
    pub async fn huoqu_fenye_huancun(
        redis_lianjie: Option<&redis_lianjie_guanli>,
        yema: u32,
        meiye_shuliang: u32
    ) -> anyhow::Result<Option<fenye_jieguo>> {
        if let Some(redis) = redis_lianjie {
            let redis_jian = Self::shengcheng_fenye_huancun_jian(yema, meiye_shuliang);

            if let Ok(Some(huancun_shuju)) = redis.huoqu(&redis_jian).await {
                if let Ok(fenye_jieguo) = serde_json::from_str::<fenye_jieguo>(&huancun_shuju) {
                    return Ok(Some(fenye_jieguo));
                }
            }
        }
        Ok(None)
    }

    /// 设置分页缓存到Redis
    pub async fn shezhi_fenye_huancun(
        redis_lianjie: Option<&redis_lianjie_guanli>,
        fenye_jieguo: &fenye_jieguo
    ) -> anyhow::Result<()> {
        if let Some(redis) = redis_lianjie {
            let redis_jian = Self::shengcheng_fenye_huancun_jian(
                fenye_jieguo.dangqian_yema,
                fenye_jieguo.meiye_shuliang
            );

            if let Ok(json_shuju) = serde_json::to_string(fenye_jieguo) {
                // 设置3天过期时间（259200秒）
                let _ = redis.shezhi_with_guoqi(&redis_jian, &json_shuju, 259200).await;
            }
        }
        Ok(())
    }

    /// 判断是否为英文类名
    pub fn shi_yingwen_leiming(guanjianci: &str) -> bool {
        guanjianci.chars().all(|c| c.is_ascii_alphanumeric() || c == '_')
    }

    /// 构建分类搜索的WHERE条件
    pub fn gouzao_fenlei_sousuo_tiaojian(canshu: &mingcheng_fenlei_lianhe_sousuo_canshu) -> (String, Vec<String>) {
        let mut where_tiaojian = Vec::new();
        let mut tiaojian_miaoshu = Vec::new();

        // 名称搜索条件
        if let Some(mingcheng_sousuo) = &canshu.mingcheng_sousuo {
            if !mingcheng_sousuo.guanjianci.trim().is_empty() {
                let shi_leiming = Self::shi_yingwen_leiming(&mingcheng_sousuo.guanjianci);

                match mingcheng_sousuo.sousuo_leixing {
                    mingcheng_sousuo_leixing::jingque => {
                        if shi_leiming {
                            where_tiaojian.push(format!(
                                "guaiwu_huizong.dbname = '{}'",
                                mingcheng_sousuo.guanjianci
                            ));
                            tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_leiming_jingque_miaoshu(&mingcheng_sousuo.guanjianci));
                        } else {
                            where_tiaojian.push(format!(
                                "(EXISTS (SELECT 1 FROM mob_name WHERE mob_name.ID = guaiwu_huizong.id AND mob_name.schinese = '{}') OR guaiwu_huizong.zhongwenming = '{}')",
                                mingcheng_sousuo.guanjianci, mingcheng_sousuo.guanjianci
                            ));
                            tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongwenming_jingque_miaoshu(&mingcheng_sousuo.guanjianci));
                        }
                    }
                    mingcheng_sousuo_leixing::mohu => {
                        if shi_leiming {
                            where_tiaojian.push(format!(
                                "(guaiwu_huizong.dbname = '{}' OR EXISTS (SELECT 1 FROM mob_name WHERE mob_name.ID = guaiwu_huizong.id AND mob_name.schinese LIKE '%{}%') OR guaiwu_huizong.zhongwenming LIKE '%{}%')",
                                mingcheng_sousuo.guanjianci, mingcheng_sousuo.guanjianci, mingcheng_sousuo.guanjianci
                            ));
                            tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_leiming_mohu_miaoshu(&mingcheng_sousuo.guanjianci));
                        } else {
                            where_tiaojian.push(format!(
                                "(EXISTS (SELECT 1 FROM mob_name WHERE mob_name.ID = guaiwu_huizong.id AND mob_name.schinese LIKE '%{}%') OR guaiwu_huizong.zhongwenming LIKE '%{}%')",
                                mingcheng_sousuo.guanjianci, mingcheng_sousuo.guanjianci
                            ));
                            tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongwenming_mohu_miaoshu(&mingcheng_sousuo.guanjianci));
                        }
                    }
                }
            }
        }

        // 辅助函数：构建字段条件
        let gouzao_ziduan_tiaojian = |ziduan_ming: &str| -> String {
            format!("({} = '1' OR {} = 'true')", ziduan_ming, ziduan_ming)
        };

        // 尺寸条件
        if let Some(chicun) = &canshu.chicun_shaixuan {
            match chicun {
                chicun::small => {
                    where_tiaojian.push(gouzao_ziduan_tiaojian("chicun_small"));
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_chicun_miaoshu(chicun).to_string());
                }
                chicun::medium => {
                    where_tiaojian.push(gouzao_ziduan_tiaojian("chicun_medium"));
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_chicun_miaoshu(chicun).to_string());
                }
                chicun::large => {
                    where_tiaojian.push(gouzao_ziduan_tiaojian("chicun_large"));
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_chicun_miaoshu(chicun).to_string());
                }
                chicun::weizhi => {
                    where_tiaojian.push(gouzao_ziduan_tiaojian("chicun_weizhi"));
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_chicun_miaoshu(chicun).to_string());
                }
            }
        }

        // 元素条件
        if let Some(yuansu) = &canshu.yuansu_shaixuan {
            match yuansu {
                yuansu::wu => {
                    where_tiaojian.push("(yuansu_wu = '1' OR yuansu_wu = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::shui => {
                    where_tiaojian.push("(yuansu_shui = '1' OR yuansu_shui = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::di => {
                    where_tiaojian.push("(yuansu_di = '1' OR yuansu_di = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::huo => {
                    where_tiaojian.push(gouzao_ziduan_tiaojian("yuansu_huo"));
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::feng => {
                    where_tiaojian.push("(yuansu_feng = '1' OR yuansu_feng = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::du => {
                    where_tiaojian.push("(yuansu_du = '1' OR yuansu_du = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::sheng => {
                    where_tiaojian.push("(yuansu_sheng = '1' OR yuansu_sheng = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::an => {
                    where_tiaojian.push("(yuansu_an = '1' OR yuansu_an = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::nian => {
                    where_tiaojian.push("(yuansu_nian = '1' OR yuansu_nian = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::busi => {
                    where_tiaojian.push("(yuansu_busi = '1' OR yuansu_busi = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::weizhi => {
                    where_tiaojian.push("(yuansu_weizhi = '1' OR yuansu_weizhi = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
            }
        }

        // 种族条件
        if let Some(zhongzu) = &canshu.zhongzu_shaixuan {
            match zhongzu {
                zhongzu::formless => {
                    where_tiaojian.push("zhongzu_formless = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::undead => {
                    where_tiaojian.push("zhongzu_undead = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::brute => {
                    where_tiaojian.push(gouzao_ziduan_tiaojian("zhongzu_brute"));
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::plant => {
                    where_tiaojian.push("zhongzu_plant = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::insect => {
                    where_tiaojian.push("zhongzu_insect = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::fish => {
                    where_tiaojian.push("zhongzu_fish = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::demon => {
                    where_tiaojian.push(gouzao_ziduan_tiaojian("zhongzu_demon"));
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::human => {
                    where_tiaojian.push("zhongzu_human = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::angel => {
                    where_tiaojian.push("zhongzu_angel = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::dragon => {
                    where_tiaojian.push("zhongzu_dragon = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::weizhi => {
                    where_tiaojian.push("zhongzu_weizhi = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
            }
        }

        let where_sql = if where_tiaojian.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_tiaojian.join(" AND "))
        };

        let tiaojian_miaoshu_str = if tiaojian_miaoshu.is_empty() {
            let (quanbu_guaiwu, _) = guaiwufenleijiexi::huoqu_tongyong_miaoshu();
            quanbu_guaiwu.to_string()
        } else {
            tiaojian_miaoshu.join(" + ")
        };

        (where_sql, vec![tiaojian_miaoshu_str])
    }

    /// 判断搜索关键词是否为英文类名
    pub fn shi_yingwen_leiming_v2(guanjianci: &str) -> bool {
        // 改进的判断逻辑：如果包含ASCII字母或下划线，且不全是小写中文，认为是类名
        guanjianci.chars().any(|c| c.is_ascii_alphanumeric() || c == '_')
    }

    /// 并发搜索mob_name表中的怪物ID
    pub async fn sousuo_mob_name_biao(
        mysql_lianjie: &mysql_lianjie_guanli,
        canshu: &mingcheng_sousuo_jiben_canshu
    ) -> anyhow::Result<Vec<i32>> {
        let shi_leiming = Self::shi_yingwen_leiming_v2(&canshu.guanjianci);

        let sql = match canshu.sousuo_leixing {
            mingcheng_sousuo_leixing::jingque => {
                if shi_leiming {
                    // 英文类名在mob_name表中不搜索，因为mob_name表没有类名字段
                    return Ok(Vec::new());
                } else {
                    // 中文名称精确搜索
                    "SELECT ID FROM mob_name WHERE schinese = ?".to_string()
                }
            }
            mingcheng_sousuo_leixing::mohu => {
                if shi_leiming {
                    // 英文类名在mob_name表中不搜索
                    return Ok(Vec::new());
                } else {
                    // 中文名称模糊搜索
                    "SELECT ID FROM mob_name WHERE schinese LIKE ?".to_string()
                }
            }
        };

        let bind_value = match canshu.sousuo_leixing {
            mingcheng_sousuo_leixing::jingque => canshu.guanjianci.clone(),
            mingcheng_sousuo_leixing::mohu => format!("%{}%", canshu.guanjianci),
        };

        match sqlx::query(&sql)
            .bind(&bind_value)
            .fetch_all(mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(rows) => {
                let mut id_liebiao = Vec::new();
                for row in rows {
                    if let Ok(id) = row.try_get::<i32, _>("ID") {
                        id_liebiao.push(id);
                    }
                }
                Ok(id_liebiao)
            }
            Err(e) => {
                guaiwushujuchuli_rizhi::sousuo_mob_name_biao_shibai(
                    0, 0, &e.to_string()
                );
                Err(e.into())
            }
        }
    }

    /// 并发搜索huizong表中的怪物ID
    pub async fn sousuo_huizong_biao(
        mysql_lianjie: &mysql_lianjie_guanli,
        canshu: &mingcheng_sousuo_jiben_canshu
    ) -> anyhow::Result<Vec<i32>> {
        let shi_leiming = Self::shi_yingwen_leiming_v2(&canshu.guanjianci);

        let sql = match canshu.sousuo_leixing {
            mingcheng_sousuo_leixing::jingque => {
                if shi_leiming {
                    // 英文类名精确搜索
                    "SELECT id FROM guaiwu_huizong WHERE dbname = ?".to_string()
                } else {
                    // 中文名称精确搜索
                    "SELECT id FROM guaiwu_huizong WHERE zhongwenming = ?".to_string()
                }
            }
            mingcheng_sousuo_leixing::mohu => {
                if shi_leiming {
                    // 英文类名精确搜索（性能考虑，类名不做模糊匹配）
                    "SELECT id FROM guaiwu_huizong WHERE dbname = ?".to_string()
                } else {
                    // 中文名称模糊搜索
                    "SELECT id FROM guaiwu_huizong WHERE zhongwenming LIKE ?".to_string()
                }
            }
        };

        let bind_value = match (canshu.sousuo_leixing.clone(), shi_leiming) {
            (mingcheng_sousuo_leixing::jingque, _) => canshu.guanjianci.clone(),
            (mingcheng_sousuo_leixing::mohu, true) => canshu.guanjianci.clone(), // 类名精确匹配
            (mingcheng_sousuo_leixing::mohu, false) => format!("%{}%", canshu.guanjianci), // 中文模糊匹配
        };

        match sqlx::query(&sql)
            .bind(&bind_value)
            .fetch_all(mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(rows) => {
                let mut id_liebiao = Vec::new();
                for row in rows {
                    if let Ok(id) = row.try_get::<i32, _>("id") {
                        id_liebiao.push(id);
                    }
                }
                Ok(id_liebiao)
            }
            Err(e) => {
                guaiwushujuchuli_rizhi::sousuo_huizong_biao_shibai(
                    0, 0, &e.to_string()
                );
                Err(e.into())
            }
        }
    }

    /// 并发搜索并合并去重结果
    /// 确保mob_name表查到的ID必须在huizong表中存在才算有效
    pub async fn bingfa_sousuo_he_quchong(
        mysql_lianjie: &mysql_lianjie_guanli,
        canshu: &mingcheng_sousuo_jiben_canshu
    ) -> anyhow::Result<Vec<i32>> {
        // 并发执行两个搜索任务
        let (mob_name_jieguo, huizong_jieguo) = tokio::try_join!(
            Self::sousuo_mob_name_biao(mysql_lianjie, canshu),
            Self::sousuo_huizong_biao(mysql_lianjie, canshu)
        )?;

        // 先将huizong表的结果放入HashSet，作为有效ID的基准
        let mut huizong_id_set = HashSet::new();
        for id in huizong_jieguo {
            huizong_id_set.insert(id);
        }

        // 验证mob_name表的结果，只保留在huizong表中存在的ID
        let mut youxiao_mob_name_id_liebiao = Vec::new();
        if !mob_name_jieguo.is_empty() {
            // 逐个检查mob_name表的ID是否在huizong表中存在
            for mob_id in mob_name_jieguo {
                let jiancha_sql = "SELECT COUNT(*) as count FROM guaiwu_huizong WHERE id = ?";

                match sqlx::query(jiancha_sql)
                    .bind(mob_id)
                    .fetch_one(mysql_lianjie.huoqu_lianjiechi()?)
                    .await
                {
                    Ok(row) => {
                        if let Ok(count) = row.try_get::<i64, _>("count") {
                            if count > 0 {
                                // 该ID在huizong表中存在，添加到有效列表
                                youxiao_mob_name_id_liebiao.push(mob_id);
                            }
                        }
                    }
                    Err(e) => {
                        guaiwushujuchuli_rizhi::bingfa_sousuo_shibai(
                            0, 0, &format!("验证mob_name表ID {}失败: {}", mob_id, e)
                        );
                        // 验证失败的ID跳过，继续处理下一个
                    }
                }
            }
        }

        // 合并有效的结果：huizong表的结果 + 验证过的mob_name表结果
        for id in youxiao_mob_name_id_liebiao {
            huizong_id_set.insert(id);
        }

        // 转换为Vec并排序
        let mut id_liebiao: Vec<i32> = huizong_id_set.into_iter().collect();
        id_liebiao.sort();

        Ok(id_liebiao)
    }
}