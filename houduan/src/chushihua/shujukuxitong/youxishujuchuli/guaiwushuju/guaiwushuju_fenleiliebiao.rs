#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use serde::{Deserialize, Serialize};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use super::guaiwushujuchuli_rizhi::guaiwushujuchuli_rizhi;
use super::guaiwufenleijiegouti::{
    chicun, yuansu, zhongzu, biaozhi, ai, guaiwushujufenleijiegou, guaiwufenleijiexi,
    guaiwu_shuju_xiang, fenye_jieguo, mingcheng_fenlei_lianhe_sousuo_canshu,
    guaiwu_tongyong_shujufangwen
};
use sqlx::{Row, Column};
use std::collections::HashMap;

/// 分类怪物数据项
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct fenlei_guaiwu_shuju_xiang {
    /// 怪物ID
    pub id: i32,
    /// 类名（Aegis_name或dbname）
    pub leiming: String,
    /// 名称（schinese或zhongwenming）
    pub mingcheng: String,
    /// 分类信息
    pub fenlei_xinxi: guaiwushujufenleijiegou,
}

/// 联合搜索参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct lianhe_sousuo_canshu {
    /// 尺寸筛选（可选）
    pub chicun_shaixuan: Option<chicun>,
    /// 元素筛选（可选）
    pub yuansu_shaixuan: Option<yuansu>,
    /// 种族筛选（可选）
    pub zhongzu_shaixuan: Option<zhongzu>,
    /// 标志筛选（可选）
    pub biaozhi_shaixuan: Option<biaozhi>,
    /// AI行为筛选（可选，支持多个）
    pub ai_shaixuan: Option<Vec<ai>>,
    /// 页码（从1开始）
    pub yema: u32,
    /// 每页数量
    pub meiye_shuliang: u32,
}

/// 分页参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct fenye_canshu {
    /// 页码（从1开始）
    pub yema: u32,
    /// 每页数量
    pub meiye_shuliang: u32,
}

/// 分类怪物分页结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct fenlei_fenye_jieguo {
    /// 分类怪物数据列表
    pub guaiwu_liebiao: Vec<fenlei_guaiwu_shuju_xiang>,
    /// 当前页码
    pub dangqian_yema: u32,
    /// 每页数量
    pub meiye_shuliang: u32,
    /// 总数量
    pub zong_shuliang: u64,
    /// 总页数
    pub zong_yeshu: u32,
    /// 搜索条件摘要
    pub sousuo_tiaojian: String,
}

/// 分类怪物数据列表管理器
pub struct guaiwushuju_fenleiliebiao {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_lianjie: Option<redis_lianjie_guanli>,
}

impl guaiwushuju_fenleiliebiao {
    /// 创建新的分类怪物数据列表实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: None,
        }
    }

    /// 创建带Redis连接的分类怪物数据列表实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_lianjie: redis_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: Some(redis_lianjie),
        }
    }



    /// 根据ID获取怪物名称信息
    pub async fn huoqu_guaiwu_mingcheng_xinxi(&self, id: i32) -> anyhow::Result<(String, String)> {
        // 首先尝试从mob_name表获取数据
        let mob_name_sql = "SELECT Aegis_name, schinese FROM mob_name WHERE ID = ?";

        match sqlx::query(mob_name_sql)
            .bind(id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(Some(row)) => {
                let aegis_name: Option<String> = row.try_get("Aegis_name").unwrap_or(None);
                let schinese: Option<String> = row.try_get("schinese").unwrap_or(None);

                let leiming = aegis_name.unwrap_or_default();
                let mingcheng = schinese.unwrap_or_default();

                Ok((leiming, mingcheng))
            }
            Ok(None) => {
                // mob_name表中没有数据，从huizong表获取
                let huizong_sql = "SELECT dbname, zhongwenming FROM guaiwu_huizong WHERE id = ?";

                match sqlx::query(huizong_sql)
                    .bind(id)
                    .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
                    .await
                {
                    Ok(Some(row)) => {
                        let dbname: Option<String> = row.try_get("dbname").unwrap_or(None);
                        let zhongwenming: Option<String> = row.try_get("zhongwenming").unwrap_or(None);

                        let leiming = dbname.unwrap_or_default();
                        let mingcheng = zhongwenming.unwrap_or_default();

                        Ok((leiming, mingcheng))
                    }
                    Ok(None) => {
                        Ok((String::new(), String::new()))
                    }
                    Err(e) => {
                        guaiwushujuchuli_rizhi::huoqu_guaiwu_mingcheng_xinxi_shibai(id, &e.to_string());
                        Err(e.into())
                    }
                }
            }
            Err(e) => {
                guaiwushujuchuli_rizhi::huoqu_guaiwu_mingcheng_xinxi_shibai(id, &e.to_string());
                Err(e.into())
            }
        }
    }

    /// 根据ID获取怪物分类数据，支持Redis缓存
    pub async fn huoqu_guaiwu_fenlei_shuju(&self, id: i32) -> anyhow::Result<Option<guaiwushujufenleijiegou>> {
        let redis_jian = format!("guaiwu_fenlei:{}", id);

        // 先尝试从Redis获取缓存数据
        if let Some(redis) = &self.redis_lianjie {
            if let Ok(Some(huancun_shuju)) = redis.huoqu(&redis_jian).await {
                if let Ok(fenlei_xinxi) = serde_json::from_str::<guaiwushujufenleijiegou>(&huancun_shuju) {
                    return Ok(Some(fenlei_xinxi));
                }
            }
        }

        // 从数据库获取分类相关字段
        let sql = r#"
            SELECT
                chicun_small, chicun_medium, chicun_large, chicun_weizhi,
                yuansu_wu, yuansu_shui, yuansu_di, yuansu_huo, yuansu_feng,
                yuansu_du, yuansu_sheng, yuansu_an, yuansu_nian, yuansu_busi, yuansu_weizhi,
                zhongzu_formless, zhongzu_undead, zhongzu_brute, zhongzu_plant, zhongzu_insect,
                zhongzu_fish, zhongzu_demon, zhongzu_human, zhongzu_angel, zhongzu_dragon, zhongzu_weizhi,
                biaozhi_normal, biaozhi_champion, biaozhi_boss, biaozhi_mvp, biaozhi_weizhi,
                ai_aggressive, ai_assist, ai_looter, ai_cast_sensor, ai_immobile, ai_weizhi
            FROM guaiwu_huizong
            WHERE id = ?
        "#;

        match sqlx::query(sql)
            .bind(id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(Some(row)) => {
                let mut shuju_map = HashMap::new();

                // 获取所有列名并转换为HashMap
                let lie_mingcheng = row.columns();
                for lie in lie_mingcheng {
                    let lie_ming = lie.name();
                    let zhi = match row.try_get::<Option<String>, _>(lie_ming) {
                        Ok(val) => val.unwrap_or_default(),
                        Err(_) => {
                            if let Ok(int_val) = row.try_get::<Option<i32>, _>(lie_ming) {
                                int_val.map(|v| v.to_string()).unwrap_or_default()
                            } else {
                                String::new()
                            }
                        }
                    };
                    shuju_map.insert(lie_ming.to_string(), zhi);
                }

                let fenlei_xinxi = guaiwufenleijiexi::gouzao_fenlei_xinxi(&shuju_map);

                // 缓存到Redis
                if let Some(redis) = &self.redis_lianjie {
                    if let Ok(json_shuju) = serde_json::to_string(&fenlei_xinxi) {
                        let _ = redis.shezhi(&redis_jian, &json_shuju).await;
                        let _ = redis.shezhi_guoqi(&redis_jian, 3600).await; // 1小时过期
                    }
                }

                Ok(Some(fenlei_xinxi))
            }
            Ok(None) => Ok(None),
            Err(e) => {
                guaiwushujuchuli_rizhi::huoqu_guaiwu_huizong_shuju_shibai(id, "分类字段", &e.to_string());
                Err(e.into())
            }
        }
    }

    /// 构建联合搜索的WHERE条件
    fn gouzao_sousuo_tiaojian(&self, canshu: &lianhe_sousuo_canshu) -> (String, Vec<String>) {
        let mut where_tiaojian = Vec::new();
        let mut tiaojian_miaoshu = Vec::new();

        // 辅助函数：构建字段条件
        let gouzao_ziduan_tiaojian = |ziduan_ming: &str| -> String {
            format!("({} = '1' OR {} = 'true')", ziduan_ming, ziduan_ming)
        };

        // 尺寸条件
        if let Some(chicun) = &canshu.chicun_shaixuan {
            match chicun {
                chicun::small => {
                    where_tiaojian.push(gouzao_ziduan_tiaojian("chicun_small"));
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_chicun_miaoshu(chicun).to_string());
                }
                chicun::medium => {
                    where_tiaojian.push(gouzao_ziduan_tiaojian("chicun_medium"));
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_chicun_miaoshu(chicun).to_string());
                }
                chicun::large => {
                    where_tiaojian.push(gouzao_ziduan_tiaojian("chicun_large"));
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_chicun_miaoshu(chicun).to_string());
                }
                chicun::weizhi => {
                    where_tiaojian.push(gouzao_ziduan_tiaojian("chicun_weizhi"));
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_chicun_miaoshu(chicun).to_string());
                }
            }
        }

        // 元素条件
        if let Some(yuansu) = &canshu.yuansu_shaixuan {
            match yuansu {
                yuansu::wu => {
                    where_tiaojian.push("(yuansu_wu = '1' OR yuansu_wu = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::shui => {
                    where_tiaojian.push("(yuansu_shui = '1' OR yuansu_shui = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::di => {
                    where_tiaojian.push("(yuansu_di = '1' OR yuansu_di = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::huo => {
                    where_tiaojian.push(gouzao_ziduan_tiaojian("yuansu_huo"));
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::feng => {
                    where_tiaojian.push("(yuansu_feng = '1' OR yuansu_feng = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::du => {
                    where_tiaojian.push("(yuansu_du = '1' OR yuansu_du = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::sheng => {
                    where_tiaojian.push("(yuansu_sheng = '1' OR yuansu_sheng = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::an => {
                    where_tiaojian.push("(yuansu_an = '1' OR yuansu_an = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::nian => {
                    where_tiaojian.push("(yuansu_nian = '1' OR yuansu_nian = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::busi => {
                    where_tiaojian.push("(yuansu_busi = '1' OR yuansu_busi = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
                yuansu::weizhi => {
                    where_tiaojian.push("(yuansu_weizhi = '1' OR yuansu_weizhi = 'true')".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_yuansu_miaoshu(yuansu).to_string());
                }
            }
        }

        // 种族条件
        if let Some(zhongzu) = &canshu.zhongzu_shaixuan {
            match zhongzu {
                zhongzu::formless => {
                    where_tiaojian.push("zhongzu_formless = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::undead => {
                    where_tiaojian.push("zhongzu_undead = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::brute => {
                    where_tiaojian.push(gouzao_ziduan_tiaojian("zhongzu_brute"));
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::plant => {
                    where_tiaojian.push("zhongzu_plant = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::insect => {
                    where_tiaojian.push("zhongzu_insect = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::fish => {
                    where_tiaojian.push("zhongzu_fish = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::demon => {
                    where_tiaojian.push(gouzao_ziduan_tiaojian("zhongzu_demon"));
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::human => {
                    where_tiaojian.push("zhongzu_human = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::angel => {
                    where_tiaojian.push("zhongzu_angel = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::dragon => {
                    where_tiaojian.push("zhongzu_dragon = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
                zhongzu::weizhi => {
                    where_tiaojian.push("zhongzu_weizhi = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_zhongzu_miaoshu(zhongzu).to_string());
                }
            }
        }

        // 标志条件
        if let Some(biaozhi) = &canshu.biaozhi_shaixuan {
            match biaozhi {
                biaozhi::normal => {
                    where_tiaojian.push("biaozhi_normal = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_biaozhi_miaoshu(biaozhi).to_string());
                }
                biaozhi::champion => {
                    where_tiaojian.push("biaozhi_champion = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_biaozhi_miaoshu(biaozhi).to_string());
                }
                biaozhi::boss => {
                    where_tiaojian.push(gouzao_ziduan_tiaojian("biaozhi_boss"));
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_biaozhi_miaoshu(biaozhi).to_string());
                }
                biaozhi::mvp => {
                    where_tiaojian.push(gouzao_ziduan_tiaojian("biaozhi_mvp"));
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_biaozhi_miaoshu(biaozhi).to_string());
                }
                biaozhi::weizhi => {
                    where_tiaojian.push("biaozhi_weizhi = '1'".to_string());
                    tiaojian_miaoshu.push(guaiwufenleijiexi::huoqu_biaozhi_miaoshu(biaozhi).to_string());
                }
            }
        }

        // AI行为条件
        if let Some(ai_liebiao) = &canshu.ai_shaixuan {
            let mut ai_tiaojian = Vec::new();
            let mut ai_miaoshu = Vec::new();

            for ai_leixing in ai_liebiao {
                match ai_leixing {
                    ai::aggressive => {
                        ai_tiaojian.push(gouzao_ziduan_tiaojian("ai_aggressive"));
                        ai_miaoshu.push(guaiwufenleijiexi::huoqu_ai_miaoshu(ai_leixing).to_string());
                    }
                    ai::assist => {
                        ai_tiaojian.push(gouzao_ziduan_tiaojian("ai_assist"));
                        ai_miaoshu.push(guaiwufenleijiexi::huoqu_ai_miaoshu(ai_leixing).to_string());
                    }
                    ai::looter => {
                        ai_tiaojian.push(gouzao_ziduan_tiaojian("ai_looter"));
                        ai_miaoshu.push(guaiwufenleijiexi::huoqu_ai_miaoshu(ai_leixing).to_string());
                    }
                    ai::cast_sensor => {
                        ai_tiaojian.push(gouzao_ziduan_tiaojian("ai_cast_sensor"));
                        ai_miaoshu.push(guaiwufenleijiexi::huoqu_ai_miaoshu(ai_leixing).to_string());
                    }
                    ai::immobile => {
                        ai_tiaojian.push(gouzao_ziduan_tiaojian("ai_immobile"));
                        ai_miaoshu.push(guaiwufenleijiexi::huoqu_ai_miaoshu(ai_leixing).to_string());
                    }
                    ai::weizhi => {
                        ai_tiaojian.push(gouzao_ziduan_tiaojian("ai_weizhi"));
                        ai_miaoshu.push(guaiwufenleijiexi::huoqu_ai_miaoshu(ai_leixing).to_string());
                    }
                }
            }

            if !ai_tiaojian.is_empty() {
                // AI条件使用OR连接（怪物可能有多个AI行为）
                where_tiaojian.push(format!("({})", ai_tiaojian.join(" OR ")));
                let (_, ai_suffix) = guaiwufenleijiexi::huoqu_tongyong_miaoshu();
                tiaojian_miaoshu.push(format!("{}{}", ai_miaoshu.join("+"), ai_suffix));
            }
        }

        let where_sql = if where_tiaojian.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_tiaojian.join(" AND "))
        };

        let tiaojian_miaoshu_str = if tiaojian_miaoshu.is_empty() {
            let (quanbu_guaiwu, _) = guaiwufenleijiexi::huoqu_tongyong_miaoshu();
            quanbu_guaiwu.to_string()
        } else {
            tiaojian_miaoshu.join(" + ")
        };

        (where_sql, vec![tiaojian_miaoshu_str])
    }

    /// 联合搜索怪物列表（分页查询，支持Redis缓存）
    pub async fn lianhe_sousuo_guaiwu_liebiao(&self, canshu: lianhe_sousuo_canshu) -> anyhow::Result<String> {
        // 参数验证
        if canshu.yema == 0 {
            let cuowu_xinxi = "页码必须大于0";
            guaiwushujuchuli_rizhi::guaiwu_liebiao_canshu_yanzheng_shibai(
                canshu.yema,
                canshu.meiye_shuliang,
                cuowu_xinxi
            );
            return Err(anyhow::anyhow!(cuowu_xinxi));
        }
        if canshu.meiye_shuliang == 0 || canshu.meiye_shuliang > 100 {
            let cuowu_xinxi = "每页数量必须在1-100之间";
            guaiwushujuchuli_rizhi::guaiwu_liebiao_canshu_yanzheng_shibai(
                canshu.yema,
                canshu.meiye_shuliang,
                cuowu_xinxi
            );
            return Err(anyhow::anyhow!(cuowu_xinxi));
        }

        // 构建搜索条件
        let (where_sql, tiaojian_miaoshu) = self.gouzao_sousuo_tiaojian(&canshu);
        let sousuo_tiaojian = tiaojian_miaoshu.join("");



        // 生成缓存键
        let huancun_jian = format!(
            "fenlei_sousuo:{}:{}:{}",
            serde_json::to_string(&canshu).unwrap_or_default().chars().take(50).collect::<String>(),
            canshu.yema,
            canshu.meiye_shuliang
        );

        // 先尝试从Redis获取缓存数据
        if let Some(redis) = &self.redis_lianjie {
            if let Ok(Some(huancun_shuju)) = redis.huoqu(&huancun_jian).await {
                if let Ok(fenye_jieguo) = serde_json::from_str::<fenlei_fenye_jieguo>(&huancun_shuju) {
                    return Ok(serde_json::to_string(&fenye_jieguo)?);
                }
            }
        }

        // 构建查询SQL
        let count_sql = if where_sql.is_empty() {
            "SELECT COUNT(*) as total FROM guaiwu_huizong".to_string()
        } else {
            format!("SELECT COUNT(*) as total FROM guaiwu_huizong {}", where_sql)
        };



        // 获取总数量
        let zong_shuliang: u64 = match sqlx::query(&count_sql)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(row) => {
                let count: i64 = row.try_get("total").unwrap_or(0);
                count as u64
            }
            Err(e) => {
                guaiwushujuchuli_rizhi::huoqu_zong_shuliang_shibai(
                    canshu.yema,
                    canshu.meiye_shuliang,
                    &e.to_string()
                );
                return Err(e.into());
            }
        };

        // 计算总页数
        let zong_yeshu = ((zong_shuliang as f64) / (canshu.meiye_shuliang as f64)).ceil() as u32;

        // 如果请求的页码超出范围，返回空结果
        if canshu.yema > zong_yeshu && zong_yeshu > 0 {
            let fenye_jieguo = fenlei_fenye_jieguo {
                guaiwu_liebiao: Vec::new(),
                dangqian_yema: canshu.yema,
                meiye_shuliang: canshu.meiye_shuliang,
                zong_shuliang,
                zong_yeshu,
                sousuo_tiaojian: sousuo_tiaojian.clone(),
            };
            return Ok(serde_json::to_string(&fenye_jieguo)?);
        }

        // 构建分页查询SQL
        let offset = (canshu.yema - 1) * canshu.meiye_shuliang;
        let data_sql = if where_sql.is_empty() {
            format!("SELECT id FROM guaiwu_huizong ORDER BY id LIMIT {} OFFSET {}",
                   canshu.meiye_shuliang, offset)
        } else {
            format!("SELECT id FROM guaiwu_huizong {} ORDER BY id LIMIT {} OFFSET {}",
                   where_sql, canshu.meiye_shuliang, offset)
        };

        // 获取ID列表
        let id_liebiao: Vec<i32> = match sqlx::query(&data_sql)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(rows) => {
                rows.into_iter()
                    .filter_map(|row| row.try_get::<i32, _>("id").ok())
                    .collect()
            }
            Err(e) => {
                guaiwushujuchuli_rizhi::huoqu_id_liebiao_shibai(
                    canshu.yema,
                    canshu.meiye_shuliang,
                    &e.to_string()
                );
                return Err(e.into());
            }
        };

        // 获取每个怪物的详细信息
        let mut guaiwu_liebiao = Vec::new();
        for id in id_liebiao {
            // 获取名称信息
            let (leiming, mingcheng) = match self.huoqu_guaiwu_mingcheng_xinxi(id).await {
                Ok(info) => info,
                Err(e) => {
                    guaiwushujuchuli_rizhi::huoqu_guaiwu_mingcheng_xinxi_shibai_with_id(
                        id,
                        canshu.yema,
                        canshu.meiye_shuliang,
                        &e.to_string()
                    );
                    return Err(e);
                }
            };

            // 获取分类信息
            let fenlei_xinxi = match self.huoqu_guaiwu_fenlei_shuju(id).await {
                Ok(Some(info)) => info,
                Ok(None) => {
                    // 如果没有分类信息，创建默认的
                    guaiwushujufenleijiegou {
                        chicun: chicun::weizhi,
                        yuansu: yuansu::weizhi,
                        zhongzu: zhongzu::weizhi,
                        biaozhi: biaozhi::weizhi,
                        ai: vec![ai::weizhi],
                    }
                }
                Err(e) => {
                    guaiwushujuchuli_rizhi::huoqu_guaiwu_fenlei_xinxi_shibai_with_id(
                        id,
                        canshu.yema,
                        canshu.meiye_shuliang,
                        &e.to_string()
                    );
                    return Err(e);
                }
            };

            guaiwu_liebiao.push(fenlei_guaiwu_shuju_xiang {
                id,
                leiming,
                mingcheng,
                fenlei_xinxi,
            });
        }

        // 构建分页结果
        let fenye_jieguo = fenlei_fenye_jieguo {
            guaiwu_liebiao,
            dangqian_yema: canshu.yema,
            meiye_shuliang: canshu.meiye_shuliang,
            zong_shuliang,
            zong_yeshu,
            sousuo_tiaojian,
        };

        // 缓存结果到Redis
        if let Some(redis) = &self.redis_lianjie {
            if let Ok(json_shuju) = serde_json::to_string(&fenye_jieguo) {
                let _ = redis.shezhi(&huancun_jian, &json_shuju).await;
                let _ = redis.shezhi_guoqi(&huancun_jian, 1800).await; // 30分钟过期
            }
        }

        Ok(serde_json::to_string(&fenye_jieguo)?)
    }

    /// 获取分类怪物列表（分页查询，支持Redis缓存）
    pub async fn huoqu_fenlei_guaiwu_liebiao(&self, fenye_canshu: fenye_canshu) -> anyhow::Result<String> {
        // 转换为联合搜索参数（无搜索条件）
        let lianhe_canshu = lianhe_sousuo_canshu {
            chicun_shaixuan: None,
            yuansu_shaixuan: None,
            zhongzu_shaixuan: None,
            biaozhi_shaixuan: None,
            ai_shaixuan: None,
            yema: fenye_canshu.yema,
            meiye_shuliang: fenye_canshu.meiye_shuliang,
        };

        self.lianhe_sousuo_guaiwu_liebiao(lianhe_canshu).await
    }

    /// 名称与分类联合搜索怪物列表（分页查询，支持Redis缓存）
    pub async fn mingcheng_fenlei_lianhe_sousuo_guaiwu_liebiao(&self, canshu: mingcheng_fenlei_lianhe_sousuo_canshu) -> anyhow::Result<String> {
        // 参数验证 - 使用统一的验证方法
        let fenye_canshu = super::guaiwufenleijiegouti::fenye_canshu {
            yema: canshu.yema,
            meiye_shuliang: canshu.meiye_shuliang,
        };
        guaiwu_tongyong_shujufangwen::yanzheng_fenye_canshu(&fenye_canshu)?;

        // 构建搜索条件
        let (where_sql, tiaojian_miaoshu) = guaiwu_tongyong_shujufangwen::gouzao_fenlei_sousuo_tiaojian(&canshu);
        let sousuo_tiaojian = tiaojian_miaoshu.join("");

        // 生成缓存键
        let huancun_jian = format!(
            "lianhe_sousuo:{}:{}:{}",
            serde_json::to_string(&canshu).unwrap_or_default().chars().take(50).collect::<String>(),
            canshu.yema,
            canshu.meiye_shuliang
        );

        // 先尝试从Redis获取缓存数据
        if let Some(redis) = &self.redis_lianjie {
            if let Ok(Some(huancun_shuju)) = redis.huoqu(&huancun_jian).await {
                if let Ok(fenye_jieguo) = serde_json::from_str::<fenye_jieguo>(&huancun_shuju) {
                    return Ok(serde_json::to_string(&fenye_jieguo)?);
                }
            }
        }

        // 构建查询SQL
        let count_sql = if where_sql.is_empty() {
            "SELECT COUNT(*) as total FROM guaiwu_huizong".to_string()
        } else {
            format!("SELECT COUNT(*) as total FROM guaiwu_huizong {}", where_sql)
        };

        // 获取总数量
        let zong_shuliang: u64 = match sqlx::query(&count_sql)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(row) => {
                let count: i64 = row.try_get("total").unwrap_or(0);
                count as u64
            }
            Err(e) => {
                guaiwushujuchuli_rizhi::huoqu_zong_shuliang_shibai(
                    canshu.yema,
                    canshu.meiye_shuliang,
                    &e.to_string()
                );
                return Err(e.into());
            }
        };

        // 计算总页数
        let zong_yeshu = if zong_shuliang == 0 {
            0
        } else {
            ((zong_shuliang - 1) / canshu.meiye_shuliang as u64 + 1) as u32
        };

        // 如果请求的页码超出范围，返回空结果
        if canshu.yema > zong_yeshu && zong_yeshu > 0 {
            let fenye_jieguo = fenye_jieguo {
                guaiwu_liebiao: Vec::new(),
                dangqian_yema: canshu.yema,
                meiye_shuliang: canshu.meiye_shuliang,
                zong_shuliang,
                zong_yeshu,
                sousuo_tiaojian: Some(sousuo_tiaojian.clone()),
            };
            return Ok(serde_json::to_string(&fenye_jieguo)?);
        }

        // 构建分页查询SQL
        let offset = (canshu.yema - 1) * canshu.meiye_shuliang;
        let data_sql = if where_sql.is_empty() {
            format!("SELECT id FROM guaiwu_huizong ORDER BY id LIMIT {} OFFSET {}",
                   canshu.meiye_shuliang, offset)
        } else {
            format!("SELECT id FROM guaiwu_huizong {} ORDER BY id LIMIT {} OFFSET {}",
                   where_sql, canshu.meiye_shuliang, offset)
        };

        // 获取ID列表
        let id_liebiao: Vec<i32> = match sqlx::query(&data_sql)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(rows) => {
                rows.into_iter()
                    .filter_map(|row| row.try_get::<i32, _>("id").ok())
                    .collect()
            }
            Err(e) => {
                guaiwushujuchuli_rizhi::huoqu_id_liebiao_shibai(
                    canshu.yema,
                    canshu.meiye_shuliang,
                    &e.to_string()
                );
                return Err(e.into());
            }
        };

        // 获取每个怪物的详细信息
        let mut guaiwu_liebiao = Vec::new();
        for id in id_liebiao {
            // 获取名称信息
            let (leiming, mingcheng) = match guaiwu_tongyong_shujufangwen::huoqu_guaiwu_mingcheng_xinxi(&self.mysql_lianjie, id).await {
                Ok(info) => info,
                Err(e) => {
                    guaiwushujuchuli_rizhi::huoqu_guaiwu_mingcheng_xinxi_shibai_with_id(
                        id,
                        canshu.yema,
                        canshu.meiye_shuliang,
                        &e.to_string()
                    );
                    return Err(e);
                }
            };

            // 获取分类信息
            let fenlei_xinxi = match self.huoqu_guaiwu_fenlei_shuju(id).await {
                Ok(Some(info)) => Some(info),
                Ok(None) => {
                    // 如果没有分类信息，创建默认的
                    Some(guaiwushujufenleijiegou {
                        chicun: chicun::weizhi,
                        yuansu: yuansu::weizhi,
                        zhongzu: zhongzu::weizhi,
                        biaozhi: biaozhi::weizhi,
                        ai: vec![ai::weizhi],
                    })
                }
                Err(e) => {
                    guaiwushujuchuli_rizhi::huoqu_guaiwu_fenlei_xinxi_shibai_with_id(
                        id,
                        canshu.yema,
                        canshu.meiye_shuliang,
                        &e.to_string()
                    );
                    return Err(e);
                }
            };

            guaiwu_liebiao.push(guaiwu_shuju_xiang {
                id,
                leiming,
                mingcheng,
                fenlei_xinxi,
            });
        }

        // 构建分页结果
        let fenye_jieguo = fenye_jieguo {
            guaiwu_liebiao,
            dangqian_yema: canshu.yema,
            meiye_shuliang: canshu.meiye_shuliang,
            zong_shuliang,
            zong_yeshu,
            sousuo_tiaojian: Some(sousuo_tiaojian),
        };

        // 缓存结果到Redis
        if let Some(redis) = &self.redis_lianjie {
            if let Ok(json_shuju) = serde_json::to_string(&fenye_jieguo) {
                let _ = redis.shezhi_with_guoqi(&huancun_jian, &json_shuju, 259200).await; // 3天缓存
            }
        }

        Ok(serde_json::to_string(&fenye_jieguo)?)
    }
}
