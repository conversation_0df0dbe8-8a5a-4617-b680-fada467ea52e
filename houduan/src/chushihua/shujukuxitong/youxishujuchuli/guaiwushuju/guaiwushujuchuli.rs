#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use super::guaiwurediskongzhi::guaiwurediskongzhi;
use super::guaiwushujuchuli_rizhi::guaiwushujuchuli_rizhi;
use super::guaiwufenleijiegouti::{
    chicun, yuansu, zhongzu, biaozhi, ai, guaiwushujufenleijiegou, guaiwufenleijiexi
};
use sqlx::{Row, Column};
use std::collections::{HashMap, HashSet};
use serde_json;
use serde::{Deserialize, Serialize};

/// 名称搜索类型
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum mingcheng_sousuo_leixing {
    /// 精确匹配
    jingque,
    /// 模糊匹配
    mohu,
}

/// 名称搜索参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct mingcheng_sousuo_canshu {
    /// 搜索关键词
    pub guanjianci: String,
    /// 搜索类型
    pub sousuo_leixing: mingcheng_sousuo_leixing,
    /// 页码（从1开始）
    pub yema: u32,
    /// 每页数量
    pub meiye_shuliang: u32,
}



/// 怪物搜索结果项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct guaiwu_sousuo_jieguo_xiang {
    /// 怪物ID
    pub id: i32,
    /// 类名（Aegis_name或dbname）
    pub leiming: String,
    /// 名称（schinese或zhongwenming）
    pub mingcheng: String,
    /// 分类信息（可选）
    pub fenlei_xinxi: Option<guaiwushujufenleijiegou>,
}

/// 怪物搜索分页结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct guaiwu_sousuo_fenye_jieguo {
    /// 怪物搜索结果列表
    pub guaiwu_liebiao: Vec<guaiwu_sousuo_jieguo_xiang>,
    /// 当前页码
    pub dangqian_yema: u32,
    /// 每页数量
    pub meiye_shuliang: u32,
    /// 总数量
    pub zong_shuliang: u64,
    /// 总页数
    pub zong_yeshu: u32,
    /// 搜索条件摘要
    pub sousuo_tiaojian: String,
}

/// 怪物数据处理类
pub struct guaiwushujuchuli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_lianjie: Option<redis_lianjie_guanli>,
}

impl guaiwushujuchuli {
    /// 创建新的怪物数据处理实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: None,
        }
    }

    /// 创建带Redis连接的怪物数据处理实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_lianjie: redis_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: Some(redis_lianjie),
        }
    }



    /// 根据id和字段名获取guaiwu_huizong表的字段值
    pub async fn huoqu_guaiwu_huizong_ziduan(&self, id: i32, ziduan_ming: &str) -> anyhow::Result<Option<String>> {
        // 构建SQL查询，使用字段名
        let sql = format!("SELECT {} FROM guaiwu_huizong WHERE id = ?", ziduan_ming);

        match sqlx::query(&sql)
            .bind(id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(Some(row)) => {
                // 尝试获取字段值，转换为字符串
                let zhi = match row.try_get::<Option<String>, _>(ziduan_ming) {
                    Ok(val) => val,
                    Err(_) => {
                        // 如果是数字类型，尝试转换
                        if let Ok(int_val) = row.try_get::<Option<i32>, _>(ziduan_ming) {
                            int_val.map(|v| v.to_string())
                        } else if let Ok(bigint_val) = row.try_get::<Option<i64>, _>(ziduan_ming) {
                            bigint_val.map(|v| v.to_string())
                        } else {
                            None
                        }
                    }
                };

                Ok(zhi)
            }
            Ok(None) => {
                Ok(None)
            }
            Err(e) => {
                guaiwushujuchuli_rizhi::huoqu_guaiwu_huizong_shuju_shibai(id, ziduan_ming, &e.to_string());
                Err(e.into())
            }
        }
    }

    /// 根据id和字段名获取mob_name表的字段值
    pub async fn huoqu_mob_name_ziduan(&self, id: i32, ziduan_ming: &str) -> anyhow::Result<Option<String>> {
        // 构建SQL查询，使用字段名
        let sql = format!("SELECT {} FROM mob_name WHERE ID = ?", ziduan_ming);

        match sqlx::query(&sql)
            .bind(id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(Some(row)) => {
                // 尝试获取字段值，转换为字符串
                let zhi = match row.try_get::<Option<String>, _>(ziduan_ming) {
                    Ok(val) => val,
                    Err(_) => {
                        // 如果是数字类型，尝试转换
                        if let Ok(int_val) = row.try_get::<Option<i32>, _>(ziduan_ming) {
                            int_val.map(|v| v.to_string())
                        } else {
                            None
                        }
                    }
                };

                Ok(zhi)
            }
            Ok(None) => {
                Ok(None)
            }
            Err(e) => {
                guaiwushujuchuli_rizhi::huoqu_guaiwu_mingcheng_shuju_shibai(id, ziduan_ming, &e.to_string());
                Err(e.into())
            }
        }
    }

    /// 根据id获取guaiwu_huizong表的全部字段数据，支持Redis缓存
    pub async fn huoqu_guaiwu_huizong_quanbu_shuju(&self, id: i32) -> anyhow::Result<Option<HashMap<String, String>>> {
        let redis_jian = format!("guaiwu_huizong:{}", id);

        // 先尝试从Redis获取缓存数据
        if let Some(redis) = &self.redis_lianjie {
            if let Ok(Some(huancun_shuju)) = redis.huoqu(&redis_jian).await {
                // 反序列化JSON数据
                if let Ok(shuju_map) = serde_json::from_str::<HashMap<String, String>>(&huancun_shuju) {
                    return Ok(Some(shuju_map));
                }
            }
        }

        // 从数据库获取数据
        let sql = "SELECT * FROM guaiwu_huizong WHERE id = ?";

        match sqlx::query(sql)
            .bind(id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(Some(row)) => {
                let mut shuju_map = HashMap::new();

                // 获取所有列名
                let lie_mingcheng = row.columns();

                for lie in lie_mingcheng {
                    let ziduan_ming = lie.name();

                    // 尝试获取字段值并转换为字符串
                    let zhi = if let Ok(val) = row.try_get::<Option<String>, _>(ziduan_ming) {
                        val.unwrap_or_default()
                    } else if let Ok(int_val) = row.try_get::<Option<i32>, _>(ziduan_ming) {
                        int_val.map(|v| v.to_string()).unwrap_or_default()
                    } else if let Ok(bigint_val) = row.try_get::<Option<i64>, _>(ziduan_ming) {
                        bigint_val.map(|v| v.to_string()).unwrap_or_default()
                    } else if let Ok(float_val) = row.try_get::<Option<f64>, _>(ziduan_ming) {
                        float_val.map(|v| v.to_string()).unwrap_or_default()
                    } else {
                        String::new()
                    };

                    shuju_map.insert(ziduan_ming.to_string(), zhi);
                }

                // 存储到Redis缓存，设置3天过期时间
                if let Some(redis) = &self.redis_lianjie {
                    if let Ok(json_shuju) = serde_json::to_string(&shuju_map) {
                        if let Err(e) = redis.shezhi_with_guoqi(&redis_jian, &json_shuju, guaiwurediskongzhi::shuju_huancun_shijian as i64).await {
                            guaiwushujuchuli_rizhi::cunchu_redis_huancun_shibai(id, &e.to_string());
                        }
                    }
                }

                Ok(Some(shuju_map))
            }
            Ok(None) => {
                Ok(None)
            }
            Err(e) => {
                guaiwushujuchuli_rizhi::huoqu_guaiwu_huizong_quanbu_shuju_shibai(id, &e.to_string());
                Err(e.into())
            }
        }
    }

    /// 根据id获取mob_name表的全部字段数据，支持Redis缓存
    pub async fn huoqu_mob_name_quanbu_shuju(&self, id: i32) -> anyhow::Result<Option<HashMap<String, String>>> {
        let redis_jian = format!("mob_name:{}", id);

        // 先尝试从Redis获取缓存数据
        if let Some(redis) = &self.redis_lianjie {
            if let Ok(Some(huancun_shuju)) = redis.huoqu(&redis_jian).await {
                // 反序列化JSON数据
                if let Ok(shuju_map) = serde_json::from_str::<HashMap<String, String>>(&huancun_shuju) {
                    return Ok(Some(shuju_map));
                }
            }
        }

        // 从数据库获取数据
        let sql = "SELECT * FROM mob_name WHERE ID = ?";

        match sqlx::query(sql)
            .bind(id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(Some(row)) => {
                let mut shuju_map = HashMap::new();

                // 获取所有列名
                let lie_mingcheng = row.columns();

                for lie in lie_mingcheng {
                    let ziduan_ming = lie.name();

                    // 尝试获取字段值并转换为字符串
                    let zhi = if let Ok(val) = row.try_get::<Option<String>, _>(ziduan_ming) {
                        val.unwrap_or_default()
                    } else if let Ok(int_val) = row.try_get::<Option<i32>, _>(ziduan_ming) {
                        int_val.map(|v| v.to_string()).unwrap_or_default()
                    } else if let Ok(bigint_val) = row.try_get::<Option<i64>, _>(ziduan_ming) {
                        bigint_val.map(|v| v.to_string()).unwrap_or_default()
                    } else if let Ok(float_val) = row.try_get::<Option<f64>, _>(ziduan_ming) {
                        float_val.map(|v| v.to_string()).unwrap_or_default()
                    } else {
                        String::new()
                    };

                    shuju_map.insert(ziduan_ming.to_string(), zhi);
                }

                // 存储到Redis缓存，设置3天过期时间
                if let Some(redis) = &self.redis_lianjie {
                    if let Ok(json_shuju) = serde_json::to_string(&shuju_map) {
                        if let Err(e) = redis.shezhi_with_guoqi(&redis_jian, &json_shuju, guaiwurediskongzhi::fenlei_huancun_shijian as i64).await {
                            guaiwushujuchuli_rizhi::cunchu_redis_huancun_shibai(id, &e.to_string());
                        }
                    }
                }

                Ok(Some(shuju_map))
            }
            Ok(None) => {
                Ok(None)
            }
            Err(e) => {
                guaiwushujuchuli_rizhi::huoqu_guaiwu_mingcheng_quanbu_shuju_shibai(id, &e.to_string());
                Err(e.into())
            }
        }
    }

    /// 根据id获取mob_name和guaiwu_huizong表的合并数据，支持Redis缓存
    /// 将两个表的数据合并存储在一个Redis值中
    /// 返回 (数据, 是否来自缓存)
    pub async fn huoqu_guaiwu_hebing_quanbu_shuju(&self, id: i32) -> anyhow::Result<Option<(HashMap<String, String>, bool)>> {
        let redis_jian = format!("guaiwu_hebing:{}", id);

        // 先尝试从Redis获取缓存数据
        if let Some(redis) = &self.redis_lianjie {
            if let Ok(Some(huancun_shuju)) = redis.huoqu(&redis_jian).await {
                // 反序列化JSON数据
                if let Ok(shuju_map) = serde_json::from_str::<HashMap<String, String>>(&huancun_shuju) {
                    return Ok(Some((shuju_map, true))); // true表示来自缓存
                }
            }
        }

        // 从数据库获取mob_name表数据
        let mob_name_sql = "SELECT * FROM mob_name WHERE ID = ?";
        let mob_name_result = sqlx::query(mob_name_sql)
            .bind(id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await;

        // 从数据库获取guaiwu_huizong表数据
        let guaiwu_huizong_sql = "SELECT * FROM guaiwu_huizong WHERE id = ?";
        let guaiwu_huizong_result = sqlx::query(guaiwu_huizong_sql)
            .bind(id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await;

        match (mob_name_result, guaiwu_huizong_result) {
            (Ok(mob_name_row), Ok(guaiwu_huizong_row)) => {
                let mut hebing_shuju_map = HashMap::new();

                // 处理mob_name表数据
                if let Some(row) = mob_name_row {
                    let lie_mingcheng = row.columns();
                    for lie in lie_mingcheng {
                        let lie_ming = lie.name();
                        let zhi = match row.try_get::<Option<String>, _>(lie_ming) {
                            Ok(val) => val.unwrap_or_default(),
                            Err(_) => {
                                if let Ok(int_val) = row.try_get::<Option<i32>, _>(lie_ming) {
                                    int_val.map(|v| v.to_string()).unwrap_or_default()
                                } else if let Ok(bigint_val) = row.try_get::<Option<i64>, _>(lie_ming) {
                                    bigint_val.map(|v| v.to_string()).unwrap_or_default()
                                } else {
                                    String::new()
                                }
                            }
                        };
                        // 为mob_name表字段添加前缀以区分
                        hebing_shuju_map.insert(format!("mob_name_{}", lie_ming), zhi);
                    }
                }

                // 处理guaiwu_huizong表数据
                if let Some(row) = guaiwu_huizong_row {
                    let lie_mingcheng = row.columns();
                    for lie in lie_mingcheng {
                        let lie_ming = lie.name();
                        let zhi = match row.try_get::<Option<String>, _>(lie_ming) {
                            Ok(val) => val.unwrap_or_default(),
                            Err(_) => {
                                if let Ok(int_val) = row.try_get::<Option<i32>, _>(lie_ming) {
                                    int_val.map(|v| v.to_string()).unwrap_or_default()
                                } else if let Ok(bigint_val) = row.try_get::<Option<i64>, _>(lie_ming) {
                                    bigint_val.map(|v| v.to_string()).unwrap_or_default()
                                } else {
                                    String::new()
                                }
                            }
                        };
                        // 为guaiwu_huizong表字段添加前缀以区分
                        hebing_shuju_map.insert(format!("guaiwu_huizong_{}", lie_ming), zhi);
                    }
                }

                // 如果有数据，存储到Redis缓存
                if !hebing_shuju_map.is_empty() {
                    if let Some(redis) = &self.redis_lianjie {
                        if let Ok(json_shuju) = serde_json::to_string(&hebing_shuju_map) {
                            if let Err(e) = redis.shezhi_with_guoqi(&redis_jian, &json_shuju, guaiwurediskongzhi::shuju_huancun_shijian as i64).await {
                                guaiwushujuchuli_rizhi::cunchu_redis_huancun_shibai(id, &e.to_string());
                            }
                        }
                    }

                    Ok(Some((hebing_shuju_map, false))) // false表示来自数据库
                } else {
                    Ok(None)
                }
            }
            (Err(e), _) | (_, Err(e)) => {
                guaiwushujuchuli_rizhi::huoqu_guaiwu_hebing_quanbu_shuju_shibai(id, &e.to_string());
                Err(e.into())
            }
        }
    }

    /// 根据ID获取怪物名称信息
    /// 类名从huizong表获取，名字从mob_name表获取
    pub async fn huoqu_guaiwu_mingcheng_xinxi(&self, id: i32) -> anyhow::Result<(String, String)> {
        // 首先从huizong表获取类名
        let huizong_sql = "SELECT dbname FROM guaiwu_huizong WHERE id = ?";

        let leiming = match sqlx::query(huizong_sql)
            .bind(id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(Some(row)) => {
                let dbname: Option<String> = row.try_get("dbname").unwrap_or(None);
                dbname.unwrap_or_default()
            }
            Ok(None) => String::new(),
            Err(e) => {
                guaiwushujuchuli_rizhi::huoqu_guaiwu_mingcheng_xinxi_shibai(id, &e.to_string());
                return Err(e.into());
            }
        };

        // 然后从mob_name表获取中文名字
        let mob_name_sql = "SELECT schinese FROM mob_name WHERE ID = ?";

        let mingcheng = match sqlx::query(mob_name_sql)
            .bind(id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(Some(row)) => {
                let schinese: Option<String> = row.try_get("schinese").unwrap_or(None);
                schinese.unwrap_or_default()
            }
            Ok(None) => {
                // 如果mob_name表没有数据，尝试从huizong表获取中文名
                let huizong_mingcheng_sql = "SELECT zhongwenming FROM guaiwu_huizong WHERE id = ?";
                match sqlx::query(huizong_mingcheng_sql)
                    .bind(id)
                    .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
                    .await
                {
                    Ok(Some(row)) => {
                        let zhongwenming: Option<String> = row.try_get("zhongwenming").unwrap_or(None);
                        zhongwenming.unwrap_or_default()
                    }
                    Ok(None) => String::new(),
                    Err(_) => String::new(),
                }
            }
            Err(e) => {
                guaiwushujuchuli_rizhi::huoqu_guaiwu_mingcheng_xinxi_shibai(id, &e.to_string());
                return Err(e.into());
            }
        };

        Ok((leiming, mingcheng))
    }



    /// 根据ID获取怪物分类数据，支持Redis缓存
    pub async fn huoqu_guaiwu_fenlei_shuju(&self, id: i32) -> anyhow::Result<Option<guaiwushujufenleijiegou>> {
        let redis_jian = format!("guaiwu_fenlei:{}", id);

        // 先尝试从Redis获取缓存数据
        if let Some(redis) = &self.redis_lianjie {
            if let Ok(Some(huancun_shuju)) = redis.huoqu(&redis_jian).await {
                if let Ok(fenlei_xinxi) = serde_json::from_str::<guaiwushujufenleijiegou>(&huancun_shuju) {
                    return Ok(Some(fenlei_xinxi));
                }
            }
        }

        // 从数据库获取分类相关字段
        let sql = r#"
            SELECT
                chicun_small, chicun_medium, chicun_large, chicun_weizhi,
                yuansu_wu, yuansu_shui, yuansu_di, yuansu_huo, yuansu_feng,
                yuansu_du, yuansu_sheng, yuansu_an, yuansu_nian, yuansu_busi, yuansu_weizhi,
                zhongzu_formless, zhongzu_undead, zhongzu_brute, zhongzu_plant, zhongzu_insect,
                zhongzu_fish, zhongzu_demon, zhongzu_human, zhongzu_angel, zhongzu_dragon, zhongzu_weizhi,
                biaozhi_normal, biaozhi_champion, biaozhi_boss, biaozhi_mvp, biaozhi_weizhi,
                ai_aggressive, ai_assist, ai_looter, ai_cast_sensor, ai_immobile, ai_weizhi
            FROM guaiwu_huizong
            WHERE id = ?
        "#;

        match sqlx::query(sql)
            .bind(id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(Some(row)) => {
                let mut shuju_map = HashMap::new();

                // 获取所有列名并转换为HashMap
                let lie_mingcheng = row.columns();
                for lie in lie_mingcheng {
                    let lie_ming = lie.name();
                    let zhi = match row.try_get::<Option<String>, _>(lie_ming) {
                        Ok(val) => val.unwrap_or_default(),
                        Err(_) => {
                            if let Ok(int_val) = row.try_get::<Option<i32>, _>(lie_ming) {
                                int_val.map(|v| v.to_string()).unwrap_or_default()
                            } else {
                                String::new()
                            }
                        }
                    };
                    shuju_map.insert(lie_ming.to_string(), zhi);
                }

                let fenlei_xinxi = guaiwufenleijiexi::gouzao_fenlei_xinxi(&shuju_map);

                // 缓存到Redis
                if let Some(redis) = &self.redis_lianjie {
                    if let Ok(json_shuju) = serde_json::to_string(&fenlei_xinxi) {
                        let _ = redis.shezhi_with_guoqi(&redis_jian, &json_shuju, guaiwurediskongzhi::liebiao_huancun_shijian as i64).await;
                    }
                }

                Ok(Some(fenlei_xinxi))
            }
            Ok(None) => Ok(None),
            Err(e) => {
                guaiwushujuchuli_rizhi::huoqu_guaiwu_huizong_shuju_shibai(id, "分类字段", &e.to_string());
                Err(e.into())
            }
        }
    }
}
