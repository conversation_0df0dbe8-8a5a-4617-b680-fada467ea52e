#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use serde::{Deserialize, Serialize};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use super::guaiwushujuchuli_rizhi::guaiwushujuchuli_rizhi;
use super::guaiwufenleijiegouti::{
    guaiwu_shuju_xiang, fenye_canshu, fenye_jieguo, guaiwu_tongyong_shujufangwen,
    mingcheng_sousuo_canshu, mingcheng_sousuo_leixing, guaiwu_sousuo_jieguo_xiang,
    guaiwu_sousuo_fenye_jieguo, guaiwu<PERSON><PERSON><PERSON><PERSON>i, mingcheng_sousuo_jiben_canshu
};
use super::guaiwurediskongzhi::guaiwuredisk<PERSON><PERSON>;
use sqlx::Row;
use serde_json;

/// 怪物数据列表管理器
pub struct guaiwushuju_guaiwuliebiao {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_lianjie: Option<redis_lianjie_guanli>,
}

impl guaiwushuju_guaiwuliebiao {
    /// 创建新的怪物数据列表实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: None,
        }
    }

    /// 创建带Redis连接的怪物数据列表实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_lianjie: redis_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: Some(redis_lianjie),
        }
    }

    /// 获取怪物总数，支持Redis缓存
    pub async fn huoqu_guaiwu_zong_shuliang(&self) -> anyhow::Result<u64> {
        guaiwu_tongyong_shujufangwen::huoqu_guaiwu_zong_shuliang(
            &self.mysql_lianjie,
            self.redis_lianjie.as_ref()
        ).await
    }

    /// 根据分页参数获取怪物ID列表
    pub async fn huoqu_guaiwu_id_liebiao(&self, fenye_canshu: &fenye_canshu) -> anyhow::Result<Vec<i32>> {
        guaiwu_tongyong_shujufangwen::huoqu_guaiwu_id_liebiao(&self.mysql_lianjie, fenye_canshu).await
    }

    /// 根据ID获取怪物名称信息
    pub async fn huoqu_guaiwu_mingcheng_xinxi(&self, id: i32) -> anyhow::Result<(String, String)> {
        guaiwu_tongyong_shujufangwen::huoqu_guaiwu_mingcheng_xinxi(&self.mysql_lianjie, id).await
    }

    /// 从Redis获取分页缓存
    async fn huoqu_fenye_huancun(&self, yema: u32, meiye_shuliang: u32) -> anyhow::Result<Option<fenye_jieguo>> {
        guaiwu_tongyong_shujufangwen::huoqu_fenye_huancun(
            self.redis_lianjie.as_ref(),
            yema,
            meiye_shuliang
        ).await
    }

    /// 设置分页缓存到Redis
    async fn shezhi_fenye_huancun(&self, fenye_jieguo: &fenye_jieguo) -> anyhow::Result<()> {
        guaiwu_tongyong_shujufangwen::shezhi_fenye_huancun(
            self.redis_lianjie.as_ref(),
            fenye_jieguo
        ).await
    }

    /// 获取怪物列表（分页查询，支持Redis缓存）
    pub async fn huoqu_guaiwu_liebiao(&self, fenye_canshu: fenye_canshu) -> anyhow::Result<String> {
        // 参数验证
        guaiwu_tongyong_shujufangwen::yanzheng_fenye_canshu(&fenye_canshu)?;

        // 先尝试从Redis获取缓存
        if let Ok(Some(huancun_jieguo)) = self.huoqu_fenye_huancun(
            fenye_canshu.yema,
            fenye_canshu.meiye_shuliang
        ).await {
            match serde_json::to_string(&huancun_jieguo) {
                Ok(json_jieguo) => return Ok(json_jieguo),
                Err(e) => {
                    guaiwushujuchuli_rizhi::guaiwu_liebiao_fenye_chaxun_shibai(
                        fenye_canshu.yema,
                        fenye_canshu.meiye_shuliang,
                        &format!("缓存数据序列化失败: {}", e)
                    );
                }
            }
        }

        // 获取总数量
        let zong_shuliang = match self.huoqu_guaiwu_zong_shuliang().await {
            Ok(shuliang) => shuliang,
            Err(e) => {
                guaiwushujuchuli_rizhi::guaiwu_liebiao_fenye_chaxun_shibai(
                    fenye_canshu.yema,
                    fenye_canshu.meiye_shuliang,
                    &format!("获取总数量失败: {}", e)
                );
                return Err(e);
            }
        };

        // 计算总页数
        let zong_yeshu = if zong_shuliang == 0 {
            0
        } else {
            ((zong_shuliang - 1) / fenye_canshu.meiye_shuliang as u64 + 1) as u32
        };

        // 检查页码是否超出范围
        if fenye_canshu.yema > zong_yeshu && zong_yeshu > 0 {
            let cuowu_xinxi = format!("页码{}超出范围，总页数为{}", fenye_canshu.yema, zong_yeshu);
            guaiwushujuchuli_rizhi::guaiwu_liebiao_canshu_yanzheng_shibai(
                fenye_canshu.yema,
                fenye_canshu.meiye_shuliang,
                &cuowu_xinxi
            );
            return Err(anyhow::anyhow!(cuowu_xinxi));
        }

        // 获取当前页的ID列表
        let id_liebiao = match self.huoqu_guaiwu_id_liebiao(&fenye_canshu).await {
            Ok(liebiao) => liebiao,
            Err(e) => {
                guaiwushujuchuli_rizhi::guaiwu_liebiao_fenye_chaxun_shibai(
                    fenye_canshu.yema,
                    fenye_canshu.meiye_shuliang,
                    &format!("获取ID列表失败: {}", e)
                );
                return Err(e);
            }
        };

        // 获取每个怪物的详细信息
        let mut guaiwu_liebiao = Vec::new();
        for id in id_liebiao {
            match self.huoqu_guaiwu_mingcheng_xinxi(id).await {
                Ok((leiming, mingcheng)) => {
                    guaiwu_liebiao.push(guaiwu_shuju_xiang {
                        id,
                        leiming,
                        mingcheng,
                        fenlei_xinxi: None, // 基础列表不包含分类信息
                    });
                }
                Err(e) => {
                    guaiwushujuchuli_rizhi::guaiwu_liebiao_fenye_chaxun_shibai(
                        fenye_canshu.yema,
                        fenye_canshu.meiye_shuliang,
                        &format!("获取怪物{}名称信息失败: {}", id, e)
                    );
                    return Err(e);
                }
            }
        }

        // 构建分页结果
        let fenye_jieguo = fenye_jieguo {
            guaiwu_liebiao,
            dangqian_yema: fenye_canshu.yema,
            meiye_shuliang: fenye_canshu.meiye_shuliang,
            zong_shuliang,
            zong_yeshu,
            sousuo_tiaojian: None, // 基础列表没有搜索条件
        };

        // 缓存结果到Redis
        if let Err(e) = self.shezhi_fenye_huancun(&fenye_jieguo).await {
            // 缓存失败不影响主要功能，只记录日志
            guaiwushujuchuli_rizhi::cunchu_redis_huancun_shibai(0, &e.to_string());
        }

        // 返回JSON字符串
        match serde_json::to_string(&fenye_jieguo) {
            Ok(json_jieguo) => Ok(json_jieguo),
            Err(e) => {
                guaiwushujuchuli_rizhi::guaiwu_liebiao_fenye_chaxun_shibai(
                    fenye_canshu.yema,
                    fenye_canshu.meiye_shuliang,
                    &format!("结果序列化失败: {}", e)
                );
                Err(e.into())
            }
        }
    }

    // ==================== 缓存管理方法 ====================

    /// 清除怪物列表相关的Redis缓存
    pub async fn qingchu_guaiwu_liebiao_huancun(&self) -> anyhow::Result<u64> {
        if let Some(redis) = &self.redis_lianjie {
            let redis_kongzhi = guaiwurediskongzhi::new(redis.clone());
            redis_kongzhi.qingchu_guaiwu_liebiao_huancun().await
        } else {
            Err(anyhow::anyhow!("Redis连接未初始化"))
        }
    }

    /// 清除怪物数据相关的Redis缓存
    pub async fn qingchu_guaiwu_shuju_huancun(&self) -> anyhow::Result<u64> {
        if let Some(redis) = &self.redis_lianjie {
            let redis_kongzhi = guaiwurediskongzhi::new(redis.clone());
            redis_kongzhi.qingchu_guaiwu_shuju_huancun().await
        } else {
            Err(anyhow::anyhow!("Redis连接未初始化"))
        }
    }

    /// 清除所有怪物相关的Redis缓存
    pub async fn qingchu_suoyou_guaiwu_huancun(&self) -> anyhow::Result<u64> {
        if let Some(redis) = &self.redis_lianjie {
            let redis_kongzhi = guaiwurediskongzhi::new(redis.clone());
            redis_kongzhi.qingchu_suoyou_guaiwu_huancun().await
        } else {
            Err(anyhow::anyhow!("Redis连接未初始化"))
        }
    }

    /// 获取怪物缓存统计信息
    pub async fn huoqu_guaiwu_huancun_tongji(&self) -> anyhow::Result<String> {
        if let Some(redis) = &self.redis_lianjie {
            let redis_kongzhi = guaiwurediskongzhi::new(redis.clone());
            redis_kongzhi.huoqu_guaiwu_huancun_tongji().await
        } else {
            Err(anyhow::anyhow!("Redis连接未初始化"))
        }
    }

    // ==================== 名称搜索方法 ====================

    /// 通过名称搜索怪物列表（分页查询，支持Redis缓存，使用并发搜索优化）
    pub async fn mingcheng_sousuo_guaiwu_liebiao(&self, canshu: mingcheng_sousuo_canshu) -> anyhow::Result<String> {
        // 参数验证 - 使用统一的验证方法
        let fenye_canshu = fenye_canshu {
            yema: canshu.yema,
            meiye_shuliang: canshu.meiye_shuliang,
        };
        guaiwu_tongyong_shujufangwen::yanzheng_fenye_canshu(&fenye_canshu)?;

        // 生成搜索条件描述
        let shi_leiming = guaiwu_tongyong_shujufangwen::shi_yingwen_leiming_v2(&canshu.guanjianci);
        let sousuo_tiaojian = match canshu.sousuo_leixing {
            mingcheng_sousuo_leixing::jingque => {
                if shi_leiming {
                    guaiwufenleijiexi::huoqu_leiming_jingque_miaoshu(&canshu.guanjianci)
                } else {
                    guaiwufenleijiexi::huoqu_zhongwenming_jingque_miaoshu(&canshu.guanjianci)
                }
            }
            mingcheng_sousuo_leixing::mohu => {
                if shi_leiming {
                    guaiwufenleijiexi::huoqu_leiming_mohu_miaoshu(&canshu.guanjianci)
                } else {
                    guaiwufenleijiexi::huoqu_zhongwenming_mohu_miaoshu(&canshu.guanjianci)
                }
            }
        };

        // 生成缓存键
        let huancun_jian = format!(
            "mingcheng_sousuo:{}:{}:{}:{}",
            canshu.guanjianci,
            match canshu.sousuo_leixing {
                mingcheng_sousuo_leixing::jingque => "jingque",
                mingcheng_sousuo_leixing::mohu => "mohu",
            },
            canshu.yema,
            canshu.meiye_shuliang
        );

        // 先尝试从Redis获取缓存数据
        if let Some(redis) = &self.redis_lianjie {
            if let Ok(Some(huancun_shuju)) = redis.huoqu(&huancun_jian).await {
                if let Ok(fenye_jieguo) = serde_json::from_str::<guaiwu_sousuo_fenye_jieguo>(&huancun_shuju) {
                    return Ok(serde_json::to_string(&fenye_jieguo)?);
                }
            }
        }

        // 创建基础搜索参数
        let jiben_canshu = mingcheng_sousuo_jiben_canshu {
            guanjianci: canshu.guanjianci.clone(),
            sousuo_leixing: canshu.sousuo_leixing.clone(),
        };

        // 使用并发搜索获取所有匹配的ID
        let quanbu_id_liebiao = match guaiwu_tongyong_shujufangwen::bingfa_sousuo_he_quchong(&self.mysql_lianjie, &jiben_canshu).await {
            Ok(liebiao) => liebiao,
            Err(e) => {
                guaiwushujuchuli_rizhi::bingfa_sousuo_shibai(
                    canshu.yema,
                    canshu.meiye_shuliang,
                    &e.to_string()
                );
                return Err(e);
            }
        };

        // 计算总数量和总页数
        let zong_shuliang = quanbu_id_liebiao.len() as u64;
        let zong_yeshu = if zong_shuliang == 0 {
            0
        } else {
            ((zong_shuliang - 1) / canshu.meiye_shuliang as u64 + 1) as u32
        };

        // 如果请求的页码超出范围，返回空结果
        if canshu.yema > zong_yeshu && zong_yeshu > 0 {
            let fenye_jieguo = guaiwu_sousuo_fenye_jieguo {
                guaiwu_liebiao: Vec::new(),
                dangqian_yema: canshu.yema,
                meiye_shuliang: canshu.meiye_shuliang,
                zong_shuliang,
                zong_yeshu,
                sousuo_tiaojian: sousuo_tiaojian.clone(),
            };
            return Ok(serde_json::to_string(&fenye_jieguo)?);
        }

        // 计算分页范围
        let kaishi_suoyin = ((canshu.yema - 1) * canshu.meiye_shuliang) as usize;
        let jieshu_suoyin = std::cmp::min(
            kaishi_suoyin + canshu.meiye_shuliang as usize,
            quanbu_id_liebiao.len()
        );

        // 获取当前页的ID列表
        let dangqian_ye_id_liebiao = if kaishi_suoyin < quanbu_id_liebiao.len() {
            quanbu_id_liebiao[kaishi_suoyin..jieshu_suoyin].to_vec()
        } else {
            Vec::new()
        };

        // 获取每个怪物的详细信息
        let mut guaiwu_liebiao = Vec::new();
        for id in dangqian_ye_id_liebiao {
            // 获取名称信息
            let (leiming, mingcheng) = match guaiwu_tongyong_shujufangwen::huoqu_guaiwu_mingcheng_xinxi(&self.mysql_lianjie, id).await {
                Ok(info) => info,
                Err(e) => {
                    guaiwushujuchuli_rizhi::huoqu_guaiwu_mingcheng_xinxi_shibai_with_id(
                        id,
                        canshu.yema,
                        canshu.meiye_shuliang,
                        &e.to_string()
                    );
                    return Err(e);
                }
            };

            guaiwu_liebiao.push(guaiwu_sousuo_jieguo_xiang {
                id,
                leiming,
                mingcheng,
                fenlei_xinxi: None, // 名称搜索不包含分类信息
            });
        }

        // 构建分页结果
        let fenye_jieguo = guaiwu_sousuo_fenye_jieguo {
            guaiwu_liebiao,
            dangqian_yema: canshu.yema,
            meiye_shuliang: canshu.meiye_shuliang,
            zong_shuliang,
            zong_yeshu,
            sousuo_tiaojian,
        };

        // 缓存结果到Redis
        if let Some(redis) = &self.redis_lianjie {
            if let Ok(json_shuju) = serde_json::to_string(&fenye_jieguo) {
                let _ = redis.shezhi_with_guoqi(&huancun_jian, &json_shuju, 259200).await; // 3天缓存
            }
        }

        Ok(serde_json::to_string(&fenye_jieguo)?)
    }
}