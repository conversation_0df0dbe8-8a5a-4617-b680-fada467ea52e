#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie_guanli;
use anyhow::Result;

/// 怪物Redis缓存控制类
pub struct guaiwurediskongzhi {
    redis_lianjie: redis_lianjie_guanli,
}

/// 怪物缓存时间常量
impl guaiwurediskongzhi {
    /// 怪物列表缓存时间：1小时（3600秒）
    pub const liebiao_huancun_shijian: u64 = 3600;
    
    /// 怪物数据缓存时间：3天（259200秒）
    pub const shuju_huancun_shijian: u64 = 259200;
    
    /// 怪物分类数据缓存时间：3天（259200秒）
    pub const fenlei_huancun_shijian: u64 = 259200;
}

impl guaiwurediskongzhi {
    /// 创建新的怪物Redis控制实例
    pub fn new(redis_lianjie: redis_lianjie_guanli) -> Self {
        Self {
            redis_lianjie,
        }
    }

    /// 清除所有怪物列表相关的Redis缓存
    /// 包括：分页列表、名称搜索、联合搜索等
    pub async fn qingchu_guaiwu_liebiao_huancun(&self) -> Result<u64> {
        let mut qingchu_shuliang = 0u64;
        
        // 清除怪物列表分页缓存
        let liebiao_keys = vec![
            "guaiwu_liebiao:*",           // 基础分页列表
            "mingcheng_sousuo:*",         // 名称搜索缓存
            "lianhe_sousuo:*",           // 联合搜索缓存
            "fenlei_liebiao:*",          // 分类列表缓存
        ];
        
        for pattern in liebiao_keys {
            match self.redis_lianjie.shanchu_by_pattern(pattern).await {
                Ok(count) => {
                    qingchu_shuliang += count;
                }
                Err(_) => {
                    // 忽略清除失败的错误，继续处理其他模式
                }
            }
        }

        // 清除总数量缓存
        if let Ok(_) = self.redis_lianjie.shanchu("guaiwu_zong_shuliang").await {
            qingchu_shuliang += 1;
        }
        Ok(qingchu_shuliang)
    }

    /// 清除所有怪物数据相关的Redis缓存
    /// 包括：怪物详细信息、分类信息、mob_name数据等
    pub async fn qingchu_guaiwu_shuju_huancun(&self) -> Result<u64> {
        let mut qingchu_shuliang = 0u64;
        
        // 清除怪物数据缓存
        let shuju_keys = vec![
            "guaiwu_fenlei:*",           // 怪物分类数据
            "mob_name:*",                // mob_name表数据
            "guaiwu_huizong:*",          // huizong表数据
            "guaiwu_xiangxi:*",          // 怪物详细信息
        ];
        
        for pattern in shuju_keys {
            match self.redis_lianjie.shanchu_by_pattern(pattern).await {
                Ok(count) => {
                    qingchu_shuliang += count;
                }
                Err(_) => {
                    // 忽略清除失败的错误，继续处理其他模式
                }
            }
        }
        Ok(qingchu_shuliang)
    }

    /// 清除所有怪物相关的Redis缓存（列表+数据）
    pub async fn qingchu_suoyou_guaiwu_huancun(&self) -> Result<u64> {
        let liebiao_count = self.qingchu_guaiwu_liebiao_huancun().await?;
        let shuju_count = self.qingchu_guaiwu_shuju_huancun().await?;

        let zong_shuliang = liebiao_count + shuju_count;

        Ok(zong_shuliang)
    }

    /// 获取怪物缓存统计信息
    pub async fn huoqu_guaiwu_huancun_tongji(&self) -> Result<String> {
        let mut tongji_xinxi = Vec::new();
        
        // 统计各类缓存数量
        let patterns = vec![
            ("怪物列表分页", "guaiwu_liebiao:*"),
            ("名称搜索", "mingcheng_sousuo:*"),
            ("联合搜索", "lianhe_sousuo:*"),
            ("分类列表", "fenlei_liebiao:*"),
            ("怪物分类数据", "guaiwu_fenlei:*"),
            ("mob_name数据", "mob_name:*"),
            ("huizong数据", "guaiwu_huizong:*"),
        ];
        
        for (mingcheng, pattern) in patterns {
            match self.redis_lianjie.count_keys_by_pattern(pattern).await {
                Ok(count) => {
                    tongji_xinxi.push(format!("{}: {} 个", mingcheng, count));
                }
                Err(_) => {
                    tongji_xinxi.push(format!("{}: 获取失败", mingcheng));
                }
            }
        }
        
        Ok(tongji_xinxi.join("\n"))
    }
}
