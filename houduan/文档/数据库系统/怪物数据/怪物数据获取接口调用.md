# 怪物数据获取接口调用文档

## 概述

本文档描述了怪物数据系统提供的所有API接口，包括怪物列表获取、详细信息查询、名称搜索、分类筛选和联合搜索等功能。

## 基础信息

- **基础路径**: `/guaiwu`
- **请求方法**: GET
- **返回格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

所有接口都返回统一的JSON格式：

```json
{
  "chenggong": true,
  "xiaoxi": "操作成功",
  "shuju": {
    "liebiao": [],
    "zongshu": 0
  }
}
```

**字段说明**:
- `chenggong`: 请求是否成功
- `xiaoxi`: 响应消息
- `shuju`: 具体数据（成功时包含，结构因接口而异）

### 错误响应格式

```json
{
  "chenggong": false,
  "xiaoxi": "错误描述信息",
  "shuju": null
}
```

## 接口列表

### 1. 怪物列表接口

获取分页的怪物列表数据。

**接口地址**: `GET /guaiwu/liebiao`

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| yema | u32 | 否 | 1 | 页码，必须大于0 |
| meiye_shuliang | u32 | 否 | 20 | 每页数量，范围1-100 |

**参数验证规则**:
- `yema`: 必须大于0
- `meiye_shuliang`: 必须在1-100之间

**请求示例**:
```
GET /guaiwu/liebiao?yema=1&meiye_shuliang=20
```

**成功响应示例**:
```json
{
  "chenggong": true,
  "xiaoxi": "获取怪物列表成功",
  "shuju": {
    "liebiao": [
      {
        "id": 1,
        "mingcheng": "哥布林",
        "dengji": 5,
        "chicun": "小型",
        "zhongzu": "人形生物",
        "cr": "1/4"
      }
    ],
    "zongshu": 150,
    "dangqian_yema": 1,
    "zongyeshu": 8
  }
}
```

**错误响应**:
- `400`: 参数验证失败
  - "页码必须大于0"
  - "每页数量必须在1-100之间"
- `500`: 服务器内部错误
  - "获取怪物列表失败: {错误详情}"
  - "解析返回数据失败: {错误详情}"

### 2. 怪物详细信息接口

根据怪物ID获取完整的怪物信息。

**接口地址**: `GET /guaiwu/xiangxi/{id}`

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | i32 | 是 | 怪物ID，必须大于0 |

**参数验证规则**:
- `id`: 必须大于0

**请求示例**:
```
GET /guaiwu/xiangxi/1
```

**成功响应示例**:
```json
{
  "chenggong": true,
  "xiaoxi": "获取怪物详细信息成功",
  "shuju": {
    "jiben_xinxi": {
      "id": 1,
      "mingcheng": "哥布林",
      "dengji": 5,
      "chicun": "小型",
      "zhongzu": "人形生物",
      "cr": "1/4",
      "jingyan_zhi": 50
    },
    "shuxing": {
      "liliang": 8,
      "minjie": 14,
      "tizhi": 10,
      "zhihui": 10,
      "ganzhi": 8,
      "meili": 8
    },
    "jineng": [
      {
        "mingcheng": "偷袭",
        "miaoshu": "在敌人未察觉时进行攻击",
        "shanghai": "1d6"
      }
    ],
    "fangyu": {
      "ac": 15,
      "hp": 7,
      "sudu": "30尺"
    }
  }
}
```

**错误响应**:
- `400`: 参数验证失败
  - "怪物ID必须大于0"
- `404`: 资源未找到
  - "未找到ID为{id}的怪物"
- `500`: 服务器内部错误
  - "获取怪物详细信息失败: {错误详情}"
  - "序列化数据失败: {错误详情}"

### 3. 怪物名称搜索接口

根据名称关键词搜索怪物。

**接口地址**: `GET /guaiwu/sousuo/mingcheng`

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| guanjianci | String | 是 | - | 搜索关键词，至少2个字符 |
| yema | u32 | 否 | 1 | 页码，必须大于0 |
| meiye_shuliang | u32 | 否 | 20 | 每页数量，范围1-100 |

**参数验证规则**:
- `guanjianci`: 不能为空，至少包含2个字符
- `yema`: 必须大于0
- `meiye_shuliang`: 必须在1-100之间

**请求示例**:
```
GET /guaiwu/sousuo/mingcheng?guanjianci=哥布林&yema=1&meiye_shuliang=10
```

**成功响应示例**:
```json
{
  "chenggong": true,
  "xiaoxi": "搜索怪物成功",
  "shuju": {
    "liebiao": [
      {
        "id": 1,
        "mingcheng": "哥布林",
        "dengji": 5
      },
      {
        "id": 15,
        "mingcheng": "哥布林战士",
        "dengji": 8
      }
    ],
    "zongshu": 5,
    "dangqian_yema": 1,
    "zongyeshu": 1,
    "sousuo_guanjianci": "哥布林"
  }
}
```

**错误响应**:
- `400`: 参数验证失败
  - "搜索关键词不能为空"
  - "搜索关键词必须至少包含2个字符"
  - "页码必须大于0"
  - "每页数量必须在1-100之间"
- `500`: 服务器内部错误
  - "搜索怪物失败: {错误详情}"
  - "解析返回数据失败: {错误详情}"

### 4. 怪物分类列表接口

获取按分类组织的怪物列表。

**接口地址**: `GET /guaiwu/fenlei/liebiao`

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| yema | u32 | 否 | 1 | 页码，必须大于0 |
| meiye_shuliang | u32 | 否 | 20 | 每页数量，范围1-100 |

**参数验证规则**:
- `yema`: 必须大于0
- `meiye_shuliang`: 必须在1-100之间

**请求示例**:
```
GET /guaiwu/fenlei/liebiao?yema=1&meiye_shuliang=20
```

**成功响应示例**:
```json
{
  "chenggong": true,
  "xiaoxi": "获取怪物分类列表成功",
  "shuju": {
    "fenlei_liebiao": [
      {
        "fenlei_mingcheng": "人形生物",
        "guaiwu_shuliang": 25,
        "guaiwu_liebiao": [
          {
            "id": 1,
            "mingcheng": "哥布林",
            "dengji": 5
          }
        ]
      },
      {
        "fenlei_mingcheng": "野兽",
        "guaiwu_shuliang": 18,
        "guaiwu_liebiao": [
          {
            "id": 10,
            "mingcheng": "狼",
            "dengji": 3
          }
        ]
      }
    ],
    "zongshu": 43,
    "dangqian_yema": 1,
    "zongyeshu": 3
  }
}
```

**错误响应**:
- `400`: 参数验证失败
  - "页码必须大于0"
  - "每页数量必须在1-100之间"
- `500`: 服务器内部错误
  - "获取怪物分类列表失败: {错误详情}"
  - "解析返回数据失败: {错误详情}"

### 5. 怪物联合搜索接口

支持多条件组合搜索怪物（当前版本仅支持名称搜索）。

**接口地址**: `GET /guaiwu/lianhe_sousuo`

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| mingcheng_guanjianci | String | 否 | - | 名称搜索关键词，至少2个字符 |
| yema | u32 | 否 | 1 | 页码，必须大于0 |
| meiye_shuliang | u32 | 否 | 20 | 每页数量，范围1-100 |

**参数验证规则**:
- `mingcheng_guanjianci`: 如果提供，至少包含2个字符
- `yema`: 必须大于0
- `meiye_shuliang`: 必须在1-100之间

**请求示例**:
```
GET /guaiwu/lianhe_sousuo?mingcheng_guanjianci=龙&yema=1&meiye_shuliang=15
```

**成功响应示例**:
```json
{
  "chenggong": true,
  "xiaoxi": "怪物联合搜索成功",
  "shuju": {
    "liebiao": [
      {
        "id": 50,
        "mingcheng": "红龙",
        "dengji": 20,
        "chicun": "巨型",
        "zhongzu": "龙类"
      },
      {
        "id": 51,
        "mingcheng": "幼龙",
        "dengji": 12,
        "chicun": "大型",
        "zhongzu": "龙类"
      }
    ],
    "zongshu": 8,
    "dangqian_yema": 1,
    "zongyeshu": 1,
    "sousuo_tiaojian": {
      "mingcheng_guanjianci": "龙"
    }
  }
}
```

**错误响应**:
- `400`: 参数验证失败
  - "搜索关键词必须至少包含2个字符"
  - "页码必须大于0"
  - "每页数量必须在1-100之间"
- `500`: 服务器内部错误
  - "怪物联合搜索失败: {错误详情}"
  - "解析返回数据失败: {错误详情}"

## HTTP状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 404 | 资源未找到（仅限详细信息接口） |
| 500 | 服务器内部错误 |

## 使用注意事项

1. **分页参数**: 所有列表接口都支持分页，建议合理设置每页数量以平衡性能和用户体验
2. **搜索关键词**: 名称搜索要求至少2个字符，支持模糊匹配
3. **缓存机制**: 系统内置Redis缓存，频繁查询的数据会被缓存以提高响应速度
4. **错误处理**: 所有接口都有完善的错误处理机制，请根据返回的错误信息进行相应处理
5. **性能优化**: 建议在前端实现适当的防抖机制，避免频繁请求

## 示例代码

### JavaScript/Fetch示例

```javascript
// 获取怪物列表
async function huoqu_guaiwu_liebiao(yema = 1, meiye_shuliang = 20) {
  try {
    const response = await fetch(`/guaiwu/liebiao?yema=${yema}&meiye_shuliang=${meiye_shuliang}`);
    const data = await response.json();

    if (data.chenggong) {
      console.log('获取成功:', data.shuju);
      return data.shuju;
    } else {
      console.error('获取失败:', data.xiaoxi);
      return null;
    }
  } catch (error) {
    console.error('请求错误:', error);
    return null;
  }
}

// 搜索怪物
async function sousuo_guaiwu(guanjianci, yema = 1) {
  try {
    const response = await fetch(`/guaiwu/sousuo/mingcheng?guanjianci=${encodeURIComponent(guanjianci)}&yema=${yema}`);
    const data = await response.json();

    if (data.chenggong) {
      return data.shuju;
    } else {
      throw new Error(data.xiaoxi);
    }
  } catch (error) {
    console.error('搜索失败:', error);
    throw error;
  }
}
```

### Python/requests示例

```python
import requests

def huoqu_guaiwu_liebiao(yema=1, meiye_shuliang=20):
    """获取怪物列表"""
    url = f"/guaiwu/liebiao"
    params = {
        'yema': yema,
        'meiye_shuliang': meiye_shuliang
    }

    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        data = response.json()

        if data['chenggong']:
            return data['shuju']
        else:
            raise Exception(data['xiaoxi'])
    except requests.RequestException as e:
        print(f"请求错误: {e}")
        return None

def huoqu_guaiwu_xiangxi(guaiwu_id):
    """获取怪物详细信息"""
    url = f"/guaiwu/xiangxi/{guaiwu_id}"

    try:
        response = requests.get(url)
        response.raise_for_status()
        data = response.json()

        if data['chenggong']:
            return data['shuju']
        else:
            raise Exception(data['xiaoxi'])
    except requests.RequestException as e:
        print(f"请求错误: {e}")
        return None
```

## 更新日志

- **v1.0.0**: 初始版本，提供基础的怪物数据获取功能
- 支持怪物列表获取、详细信息查询、名称搜索、分类筛选和联合搜索
- 集成Redis缓存机制提升性能
- 完善的参数验证和错误处理机制