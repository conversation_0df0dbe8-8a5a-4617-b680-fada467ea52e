# 怪物数据获取接口调用文档

## 概述

本文档描述了怪物数据系统提供的所有API接口，包括怪物列表获取、详细信息查询、名称搜索、分类筛选和联合搜索等功能。

## 基础信息

- **基础路径**: `/guaiwu`
- **请求方法**: GET
- **返回格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

所有接口都返回统一的JSON格式：

```json
{
  "chenggong": true,
  "xiaoxi": "操作成功",
  "shuju": {
    "liebiao": [],
    "zongshu": 0
  }
}
```

**字段说明**:
- `chenggong`: 请求是否成功
- `xiaoxi`: 响应消息
- `shuju`: 具体数据（成功时包含，结构因接口而异）

### 错误响应格式

```json
{
  "chenggong": false,
  "xiaoxi": "错误描述信息",
  "shuju": null
}
```

## 接口列表

### 1. 怪物列表接口

获取分页的怪物列表数据。

**接口地址**: `GET /guaiwu/liebiao`

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| yema | u32 | 否 | 1 | 页码，必须大于0 |
| meiye_shuliang | u32 | 否 | 20 | 每页数量，范围1-100 |

**参数验证规则**:
- `yema`: 必须大于0
- `meiye_shuliang`: 必须在1-100之间

**请求示例**:
```
GET /guaiwu/liebiao?yema=1&meiye_shuliang=20
```

**成功响应示例**:
```json
{
  "chenggong": true,
  "xiaoxi": "获取怪物列表成功",
  "shuju": {
    "liebiao": [
      {
        "id": 1,
        "mingcheng": "哥布林",
        "dengji": 5,
        "chicun": "小型",
        "zhongzu": "人形生物",
        "cr": "1/4"
      }
    ],
    "zongshu": 150,
    "dangqian_yema": 1,
    "zongyeshu": 8
  }
}
```

**错误响应**:
- `400`: 参数验证失败
  - "页码必须大于0"
  - "每页数量必须在1-100之间"
- `500`: 服务器内部错误
  - "获取怪物列表失败: {错误详情}"
  - "解析返回数据失败: {错误详情}"

### 2. 怪物详细信息接口

根据怪物ID获取完整的怪物信息。

**接口地址**: `GET /guaiwu/xiangxi/{id}`

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | i32 | 是 | 怪物ID，必须大于0 |

**参数验证规则**:
- `id`: 必须大于0

**请求示例**:
```
GET /guaiwu/xiangxi/1
```

**成功响应示例**:
```json
{
  "chenggong": true,
  "xiaoxi": "获取怪物详细信息成功",
  "shuju": {
    "jiben_xinxi": {
      "id": 1,
      "mingcheng": "哥布林",
      "dengji": 5,
      "chicun": "小型",
      "zhongzu": "人形生物",
      "cr": "1/4",
      "jingyan_zhi": 50
    },
    "shuxing": {
      "liliang": 8,
      "minjie": 14,
      "tizhi": 10,
      "zhihui": 10,
      "ganzhi": 8,
      "meili": 8
    },
    "jineng": [
      {
        "mingcheng": "偷袭",
        "miaoshu": "在敌人未察觉时进行攻击",
        "shanghai": "1d6"
      }
    ],
    "fangyu": {
      "ac": 15,
      "hp": 7,
      "sudu": "30尺"
    }
  }
}
```

**错误响应**:
- `400`: 参数验证失败
  - "怪物ID必须大于0"
- `404`: 资源未找到
  - "未找到ID为{id}的怪物"
- `500`: 服务器内部错误
  - "获取怪物详细信息失败: {错误详情}"
  - "序列化数据失败: {错误详情}"

### 3. 怪物名称搜索接口

根据名称关键词搜索怪物，支持精确搜索和模糊搜索。

**接口地址**: `GET /guaiwu/sousuo/mingcheng`

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| guanjianci | String | 是 | - | 搜索关键词，至少2个字符 |
| sousuo_leixing | String | 否 | mohu | 搜索类型：jingque(精确)、mohu(模糊) |
| yema | u32 | 否 | 1 | 页码，必须大于0 |
| meiye_shuliang | u32 | 否 | 20 | 每页数量，范围1-100 |

**参数验证规则**:
- `guanjianci`: 不能为空，至少包含2个字符
- `sousuo_leixing`: 支持 "jingque"/"精确"（精确匹配）、"mohu"/"模糊"（模糊匹配，默认）
- `yema`: 必须大于0
- `meiye_shuliang`: 必须在1-100之间

**搜索类型说明**:
- **精确搜索(jingque)**: 完全匹配怪物名称或类名
- **模糊搜索(mohu)**: 包含关键词的怪物名称或类名

**请求示例**:
```
# 模糊搜索（默认）
GET /guaiwu/sousuo/mingcheng?guanjianci=哥布林&yema=1&meiye_shuliang=10

# 精确搜索
GET /guaiwu/sousuo/mingcheng?guanjianci=哥布林&sousuo_leixing=jingque&yema=1&meiye_shuliang=10

# 英文类名精确搜索
GET /guaiwu/sousuo/mingcheng?guanjianci=PORING&sousuo_leixing=jingque
```

**成功响应示例**:
```json
{
  "chenggong": true,
  "xiaoxi": "搜索怪物成功",
  "shuju": {
    "liebiao": [
      {
        "id": 1,
        "mingcheng": "哥布林",
        "dengji": 5
      },
      {
        "id": 15,
        "mingcheng": "哥布林战士",
        "dengji": 8
      }
    ],
    "zongshu": 5,
    "dangqian_yema": 1,
    "zongyeshu": 1,
    "sousuo_guanjianci": "哥布林"
  }
}
```

**错误响应**:
- `400`: 参数验证失败
  - "搜索关键词不能为空"
  - "搜索关键词必须至少包含2个字符"
  - "无效的搜索类型: {类型}，支持的类型: jingque(精确), mohu(模糊)"
  - "页码必须大于0"
  - "每页数量必须在1-100之间"
- `500`: 服务器内部错误
  - "搜索怪物失败: {错误详情}"
  - "解析返回数据失败: {错误详情}"

### 4. 怪物分类列表接口

获取按分类组织的怪物列表。

**接口地址**: `GET /guaiwu/fenlei/liebiao`

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| yema | u32 | 否 | 1 | 页码，必须大于0 |
| meiye_shuliang | u32 | 否 | 20 | 每页数量，范围1-100 |

**参数验证规则**:
- `yema`: 必须大于0
- `meiye_shuliang`: 必须在1-100之间

**请求示例**:
```
GET /guaiwu/fenlei/liebiao?yema=1&meiye_shuliang=20
```

**成功响应示例**:
```json
{
  "chenggong": true,
  "xiaoxi": "获取怪物分类列表成功",
  "shuju": {
    "fenlei_liebiao": [
      {
        "fenlei_mingcheng": "人形生物",
        "guaiwu_shuliang": 25,
        "guaiwu_liebiao": [
          {
            "id": 1,
            "mingcheng": "哥布林",
            "dengji": 5
          }
        ]
      },
      {
        "fenlei_mingcheng": "野兽",
        "guaiwu_shuliang": 18,
        "guaiwu_liebiao": [
          {
            "id": 10,
            "mingcheng": "狼",
            "dengji": 3
          }
        ]
      }
    ],
    "zongshu": 43,
    "dangqian_yema": 1,
    "zongyeshu": 3
  }
}
```

**错误响应**:
- `400`: 参数验证失败
  - "页码必须大于0"
  - "每页数量必须在1-100之间"
- `500`: 服务器内部错误
  - "获取怪物分类列表失败: {错误详情}"
  - "解析返回数据失败: {错误详情}"

### 5. 怪物联合搜索接口

支持名称+分类的多条件组合搜索，可以按尺寸、元素、种族、标志、AI等属性筛选怪物。

**接口地址**: `GET /guaiwu/lianhe_sousuo`

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| mingcheng_guanjianci | String | 否 | - | 名称搜索关键词，至少2个字符 |
| sousuo_leixing | String | 否 | mohu | 搜索类型：jingque(精确)、mohu(模糊) |
| chicun_shaixuan | String | 否 | - | 尺寸筛选：small(小型)、medium(中型)、large(大型)、weizhi(未知) |
| yuansu_shaixuan | String | 否 | - | 元素筛选：wu(无)、shui(水)、di(地)、huo(火)、feng(风)、du(毒)、sheng(圣)、an(暗)、nian(念)、busi(不死)、weizhi(未知) |
| zhongzu_shaixuan | String | 否 | - | 种族筛选：formless(无形)、undead(不死)、brute(动物)、plant(植物)、insect(昆虫)、fish(鱼类)、demon(恶魔)、human(人类)、angel(天使)、dragon(龙族)、weizhi(未知) |
| biaozhi_shaixuan | String | 否 | - | 标志筛选：normal(普通)、champion(精英)、boss(BOSS)、mvp(MVP)、weizhi(未知) |
| ai_shaixuan | String | 否 | - | AI筛选：aggressive(主动攻击)、assist(协助)、looter(掠夺)、cast_sensor(感知施法)、immobile(不移动)、weizhi(未知)，多个用逗号分隔 |
| yema | u32 | 否 | 1 | 页码，必须大于0 |
| meiye_shuliang | u32 | 否 | 20 | 每页数量，范围1-100 |

**参数验证规则**:
- `mingcheng_guanjianci`: 如果提供，至少包含2个字符
- `sousuo_leixing`: 支持 "jingque"/"精确"（精确匹配）、"mohu"/"模糊"（模糊匹配，默认）
- 分类参数支持英文和中文两种格式
- `ai_shaixuan`: 支持多个AI类型，用逗号分隔，如 "aggressive,assist"
- `yema`: 必须大于0
- `meiye_shuliang`: 必须在1-100之间

**请求示例**:
```
# 基础名称搜索
GET /guaiwu/lianhe_sousuo?mingcheng_guanjianci=龙&yema=1&meiye_shuliang=15

# 精确名称搜索
GET /guaiwu/lianhe_sousuo?mingcheng_guanjianci=红龙&sousuo_leixing=jingque

# 按尺寸筛选大型怪物
GET /guaiwu/lianhe_sousuo?chicun_shaixuan=large

# 按元素筛选火属性怪物
GET /guaiwu/lianhe_sousuo?yuansu_shaixuan=huo

# 组合搜索：大型火属性龙族怪物
GET /guaiwu/lianhe_sousuo?chicun_shaixuan=large&yuansu_shaixuan=huo&zhongzu_shaixuan=dragon

# 复杂组合：名称包含"龙"的大型火属性BOSS级龙族怪物
GET /guaiwu/lianhe_sousuo?mingcheng_guanjianci=龙&chicun_shaixuan=large&yuansu_shaixuan=huo&zhongzu_shaixuan=dragon&biaozhi_shaixuan=boss

# AI筛选：主动攻击且会协助的怪物
GET /guaiwu/lianhe_sousuo?ai_shaixuan=aggressive,assist

# 中文参数示例
GET /guaiwu/lianhe_sousuo?chicun_shaixuan=大型&yuansu_shaixuan=火&zhongzu_shaixuan=龙族
```

**成功响应示例**:
```json
{
  "chenggong": true,
  "xiaoxi": "怪物联合搜索成功",
  "shuju": {
    "liebiao": [
      {
        "id": 50,
        "mingcheng": "红龙",
        "dengji": 20,
        "chicun": "巨型",
        "zhongzu": "龙类"
      },
      {
        "id": 51,
        "mingcheng": "幼龙",
        "dengji": 12,
        "chicun": "大型",
        "zhongzu": "龙类"
      }
    ],
    "zongshu": 8,
    "dangqian_yema": 1,
    "zongyeshu": 1,
    "sousuo_tiaojian": {
      "mingcheng_guanjianci": "龙"
    }
  }
}
```

**错误响应**:
- `400`: 参数验证失败
  - "搜索关键词必须至少包含2个字符"
  - "无效的搜索类型: {类型}，支持的类型: jingque(精确), mohu(模糊)"
  - "无效的尺寸类型: {类型}，支持的类型: small(小型), medium(中型), large(大型), weizhi(未知)"
  - "无效的元素类型: {类型}，支持的类型: wu(无), shui(水), di(地), huo(火), feng(风), du(毒), sheng(圣), an(暗), nian(念), busi(不死), weizhi(未知)"
  - "无效的种族类型: {类型}，支持的类型: formless(无形), undead(不死), brute(动物), plant(植物), insect(昆虫), fish(鱼类), demon(恶魔), human(人类), angel(天使), dragon(龙族), weizhi(未知)"
  - "无效的标志类型: {类型}，支持的类型: normal(普通), champion(精英), boss(BOSS), mvp(MVP), weizhi(未知)"
  - "无效的AI类型: {类型}，支持的类型: aggressive(主动攻击), assist(协助), looter(掠夺), cast_sensor(感知施法), immobile(不移动), weizhi(未知)"
  - "页码必须大于0"
  - "每页数量必须在1-100之间"
- `500`: 服务器内部错误
  - "怪物联合搜索失败: {错误详情}"
  - "解析返回数据失败: {错误详情}"

## HTTP状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 404 | 资源未找到（仅限详细信息接口） |
| 500 | 服务器内部错误 |

## 使用注意事项

1. **分页参数**: 所有列表接口都支持分页，建议合理设置每页数量以平衡性能和用户体验
2. **搜索关键词**: 名称搜索要求至少2个字符，支持精确匹配和模糊匹配两种模式
3. **搜索类型**:
   - **精确搜索**: 完全匹配怪物名称或类名，适用于已知确切名称的查询
   - **模糊搜索**: 包含关键词的怪物名称或类名，适用于部分记忆或探索性查询
4. **分类筛选**:
   - 支持按尺寸、元素、种族、标志、AI等多维度筛选
   - 参数支持英文和中文两种格式，如 "large"/"大型"
   - 可以组合多个筛选条件，系统会进行AND逻辑组合
   - AI筛选支持多个值，用逗号分隔
5. **联合搜索**: 可以同时使用名称搜索和分类筛选，实现精准查找
6. **缓存机制**: 系统内置Redis缓存，频繁查询的数据会被缓存以提高响应速度
7. **错误处理**: 所有接口都有完善的错误处理机制，请根据返回的错误信息进行相应处理
8. **性能优化**: 建议在前端实现适当的防抖机制，避免频繁请求

## 分类筛选详细说明

### 怪物属性分类

**尺寸分类**:
- `small` / `小型`: 小型怪物
- `medium` / `中型`: 中型怪物
- `large` / `大型`: 大型怪物
- `weizhi` / `未知`: 未知尺寸

**元素分类**:
- `wu` / `无`: 无属性
- `shui` / `水`: 水属性
- `di` / `地`: 地属性
- `huo` / `火`: 火属性
- `feng` / `风`: 风属性
- `du` / `毒`: 毒属性
- `sheng` / `圣`: 圣属性
- `an` / `暗`: 暗属性
- `nian` / `念`: 念属性
- `busi` / `不死`: 不死属性
- `weizhi` / `未知`: 未知属性

**种族分类**:
- `formless` / `无形`: 无形种族
- `undead` / `不死`: 不死种族
- `brute` / `动物`: 动物种族
- `plant` / `植物`: 植物种族
- `insect` / `昆虫`: 昆虫种族
- `fish` / `鱼类`: 鱼类种族
- `demon` / `恶魔`: 恶魔种族
- `human` / `人类`: 人类种族
- `angel` / `天使`: 天使种族
- `dragon` / `龙族`: 龙族
- `weizhi` / `未知`: 未知种族

**标志分类**:
- `normal` / `普通`: 普通怪物
- `champion` / `精英`: 精英怪物
- `boss` / `BOSS`: BOSS级怪物
- `mvp` / `MVP`: MVP级怪物
- `weizhi` / `未知`: 未知标志

**AI行为分类**:
- `aggressive` / `主动攻击`: 主动攻击玩家
- `assist` / `协助`: 协助其他怪物
- `looter` / `掠夺`: 掠夺行为
- `cast_sensor` / `感知施法`: 感知施法行为
- `immobile` / `不移动`: 不移动
- `weizhi` / `未知`: 未知AI行为

### 组合搜索示例

**按单一属性筛选**:
```
# 查找所有大型怪物
GET /guaiwu/lianhe_sousuo?chicun_shaixuan=large

# 查找所有火属性怪物
GET /guaiwu/lianhe_sousuo?yuansu_shaixuan=huo

# 查找所有龙族怪物
GET /guaiwu/lianhe_sousuo?zhongzu_shaixuan=dragon
```

**多属性组合筛选**:
```
# 查找大型火属性怪物
GET /guaiwu/lianhe_sousuo?chicun_shaixuan=large&yuansu_shaixuan=huo

# 查找大型火属性龙族BOSS
GET /guaiwu/lianhe_sousuo?chicun_shaixuan=large&yuansu_shaixuan=huo&zhongzu_shaixuan=dragon&biaozhi_shaixuan=boss

# 查找主动攻击且会协助的怪物
GET /guaiwu/lianhe_sousuo?ai_shaixuan=aggressive,assist
```

**名称+分类组合搜索**:
```
# 查找名称包含"龙"的大型怪物
GET /guaiwu/lianhe_sousuo?mingcheng_guanjianci=龙&chicun_shaixuan=large

# 精确查找名为"红龙"的火属性龙族怪物
GET /guaiwu/lianhe_sousuo?mingcheng_guanjianci=红龙&sousuo_leixing=jingque&yuansu_shaixuan=huo&zhongzu_shaixuan=dragon
```

## 更新日志

- **v1.2.0**: 完整分类搜索功能
  - 联合搜索接口支持完整的分类筛选功能
  - 新增尺寸、元素、种族、标志、AI等多维度筛选参数
  - 支持多个筛选条件的AND逻辑组合
  - AI筛选支持多个值的组合（逗号分隔）
  - 所有分类参数支持英文和中文两种格式
  - 移除代码示例，专注于接口调用说明

- **v1.1.0**: 增强搜索功能
  - 名称搜索接口新增 `sousuo_leixing` 参数，支持精确搜索和模糊搜索
  - 联合搜索接口新增 `sousuo_leixing` 参数，支持精确搜索和模糊搜索
  - 优化搜索逻辑，支持英文类名和中文名称的不同搜索策略

- **v1.0.0**: 初始版本，提供基础的怪物数据获取功能
  - 支持怪物列表获取、详细信息查询、名称搜索、分类筛选和联合搜索
  - 集成Redis缓存机制提升性能
  - 完善的参数验证和错误处理机制