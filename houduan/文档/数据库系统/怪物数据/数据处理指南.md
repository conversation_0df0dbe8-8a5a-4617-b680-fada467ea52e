# 怪物数据处理指南

本文档详细说明了如何使用 `guaiwushuju` 模块来处理和访问游戏中的怪物数据。

在使用本模块前，请确保已阅读 **[怪物数据架构](./怪物数据架构.md)** 文档以了解其内部结构。

## 核心用法

本模块的核心功能围绕着几个管理类展开：

- `guaiwu_liebiao_guanli`: 用于基础的列表查询和按名称搜索。
- `guaiwu_fenlei_liebiao_guanli`: 用于按分类筛选和联合搜索。
- `guaiwu_chuliqi`: 用于获取单个怪物的详细数据。
- `guaiwu_redis_kongzhi`: 用于管理 Redis 缓存。

---

## 1. 获取怪物列表（基础）

使用 `guaiwu_liebiao_guanli` 可以实现分页查询和按名称搜索。

### 示例：分页获取所有怪物

```rust
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwu_liebiao_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwufenleijiegouti::fenye_canshu;

// 假设 mysql_lianjie_guanli 已经初始化
let liebiao_guanli = guaiwu_liebiao_guanli::new(mysql_lianjie_guanli);

let canshu = fenye_canshu {
    yema: 1,
    meiye_shuliang: 20,
};

match liebiao_guanli.huoqu_guaiwu_liebiao(canshu).await {
    Ok(json_result) => {
        // json_result 是一个包含分页信息的 JSON 字符串
        println!("获取怪物列表成功: {}", json_result);
    }
    Err(e) => {
        eprintln!("获取怪物列表失败: {}", e);
    }
}```

### 示例：按中文名称模糊搜索

```rust
use crate::chushihua::```rust
use crate::chushihua::