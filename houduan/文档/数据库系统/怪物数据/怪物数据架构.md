# 怪物数据架构

本文档详细描述了怪物数据模块的内部代码结构，旨在阐明每个文件的职责和核心功能。

## 文件结构概览

```
guaiwushuju/
├── mod.rs                      # 模块声明和公共导出
├── guaiwufenleijiegouti.rs     # 定义所有数据结构（枚举、结构体）和通用工具
├── guaiwushuju_guaiwuliebiao.rs # 负责处理基础的怪物列表查询（如分页）
├── guaiwushuju_fenleiliebiao.rs # 负责处理按分类筛选的怪物列表查询
├── guaiwurediskongzhi.rs       # 封装了所有与 Redis 缓存相关的操作
├── guaiwushujuchuli.rs         # 封装了对单个怪物数据的核心处理逻辑
└── guaiwushujuchuli_rizhi.rs   # 提供统一的日志记录功能
```

---

### 1. `mod.rs`

- **职责**: 作为 `guaiwushuju` 模块的入口，声明所有子模块并使用 `pub use` 将核心的结构体和方法导出，方便外部调用。

---

### 2. `guaiwufenleijiegouti.rs`

- **职责**: 定义了整个怪物数据模块所需的所有数据结构和通用工具函数。
- **核心内容**:
  - **枚举 (Enums)**: 定义了怪物的各种分类，如 `chicun` (尺寸), `yuansu` (元素), `zhongzu` (种族), `biaozhi` (标志), `ai` (AI行为)。
  - **核心结构体 (Structs)**:
    - `guaiwushujufenleijiegou`: 存储一个怪物的完整分类信息。
    - `guaiwu_shuju_xiang`: 统一的怪物数据项结构，用于各种查询结果。
    - `fenye_canshu`, `fenye_jieguo`: 标准的分页参数和返回结果。
    - `mingcheng_fenlei_lianhe_sousuo_canshu`: 用于组合搜索的参数结构体。
  - **工具类**:
    - `guaiwufenleijiexi`: 提供从数据库原始数据解析为枚举类型的方法。
    - `guaiwu_tongyong_shujufangwen`: 包含跨模块复用的通用数据库访问逻辑，如参数验证、并发搜索等。

---

### 3. `guaiwushuju_guaiwuliebiao.rs`

- **职责**: 专门负责处理**基础的、不带复杂分类筛选**的怪物列表查询。
- **核心功能**:
  - 提供按分页获取怪物列表的功能。
  - 提供按怪物名称（类名或中文名）进行精确或模糊搜索的功能。
  - 内部调用 `guaiwu_tongyong_shujufangwen` 来执行实际的数据库查询和并发优化。

---

### 4. `guaiwushuju_fenleiliebiao.rs`

- **职责**: 专门负责处理**带有分类筛选条件**的复杂怪物列表查询。
- **核心功能**:
  - 支持根据尺寸、元素、种族、标志、AI行为等一个或多个条件进行组合筛选。
  - 能够将名称搜索与分类筛选结合起来，执行最高级的联合搜索。
  - 负责构建复杂的 SQL `WHERE` 子句。

---

### 5. `guaiwurediskongzhi.rs`

- **职责**: 封装了所有与 `Redis` 缓存相关的底层操作，提供一个清晰的接口来管理怪物数据的缓存。
- **核心功能**:
  - **缓存清除**: 提供 `qingchu_guaiwu_liebiao_huancun` 和 `qingchu_guaiwu_shuju_huancun` 等方法，用于在数据更新后精确地清除相关缓存。
  - **缓存统计**: 提供 `huoqu_guaiwu_huancun_tongji` 方法，用于监控缓存使用情况。
  - **常量定义**: 定义了不同类型缓存的过期时间。

---

### 6. `guaiwushujuchuli.rs`

- **职责**: 封装了对**单个怪物**数据的核心处理逻辑。
- **核心功能**:
  - `huoqu_guaiwu_huizong_quanbu_shuju`: 从 `guaiwu_huizong` 表获取一个怪物的完整数据。
  - `huoqu_mob_name_quanbu_shuju`: 从 `mob_name` 表获取一个怪物的多语言名称数据。
  - `huoqu_guaiwu_hebing_quanbu_shuju`: 将两个表的数据合并，并进行缓存。
  - **数据同步**: 负责将从 `MySQL` 读取的数据写入 `Redis` 缓存。

---

### 7. `guaiwushujuchuli_rizhi.rs`

- **职责**: 提供一套静态方法，用于记录本模块中发生的各种错误和信息。
- **核心功能**:
  - 将日志消息格式化后，统一调用 `shujukuxitong_rizhi_cuowu` 或 `shujukuxitong_rizhi_xinxi`，确保日志格式的一致性。